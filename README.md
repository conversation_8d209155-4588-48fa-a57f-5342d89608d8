# PyQt CAN Tool

This is a PyQt desktop application template that supports:
- CAN log analysis
- Firmware file modification (SREC, HEX, BIN)
- DLL checking
- CRC calculation

## Folder Structure
        pyqt_can_tool/
        │── main.py                  # Entry point of your PyQt app
        │── requirements.txt         # Dependencies (PyQt6, python-can, etc.)
        │── README.md                # Project documentation
        │
        ├── ui/                      # UI design files (.ui from Qt Designer)

        ├── views/                   # Python code for UI (converted from .ui or custom)
        │
        ├── controllers/             # Connects views with services (business logic)
        │
        ├── services/                # Core backend functionality
        │   ├── can/
        │   │
        │   ├── firmware/
        │   │
        │   ├── dll/
        │   │
        │   └── crc/
        │
        ├── models/                  # Data structures
        │
        ├── tests/                   # Unit & integration tests
        │
        └── assets/                  # Static files (icons, sample logs, firmware)
            ├── icons/
            └── samples/

## Usage
### From source
1. **Clone the Repository**:
```
git clone https://github.com/Vietsol-Co-Ltd/MC-Flashing.git
cd MC-Flashing
```

2. **Set Up the Environment**:\
Install dependencies and configure the environment:
```
pip install -r requirements.txt
```

3. **Run the mini tool**:\
```
python main.py
```

4. **Build the executable**:\
```
pyinstaller --onefile --icon=.\assets\icons\logo_icon.ico main.py
```

### From release binary
```
.\main.exe
```
