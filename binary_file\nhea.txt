Còn minitool thứ 2 là để phần tích log can flash và có thể xuất được file software từ log can record đ<PERSON><PERSON>c ạ, hiện tại ý tưởng hiện tại của em là có thể dùng nó để phân tích file log can flash của mấy sequence lạ để tạo nhanh được được sequence từ việc phân tích LOG CAN đó.
Sẽ kiểu nhập, response/request/functional ID/P2/P2* vào, chọn chn cần check. 
Sau đó tool sẽ quét lần lượt từ cái request ID đầu tiên -> Check response.
Sau khi check được response sẽ check request gần nhất ngay sau đó, rồi cứ thế đến hết.
Rồi nó xuất ra 1 file y như report sau khi mình flash xong.
Nếu có thể thì sẽ chuyển phần data log can thành file software luôn, check theo 34-36-37, cứ 1 cụm 3 service này xuất hiện sẽ export thành 1 file software. (Thật ra cái $35 - 36 là kiểu ngược lại của mình gửi data vào cho ECU thì là ECU gửi data cho mình, nếu làm được phần này thì sau này có thể phát triển để cover cho $35 nếu cần cho sau này).

Còn đây là 1 report sau 1 lần flash bằng tool xuất ra ạ.
Data vốn có hết trong file excel như này ạ, trên cơ sở mà mọi người phần tích được từ log can ngược ra lại được report như này thì đã là có full data rồi ạ.
Start address thì có thể lấy từ $34 ạ nên nhìn chung là từ 2 cái này là đủ thông tin cần thiết để export ra được file software ạ.

Các nguyên nhân e muốn chạy các mini tool này ạ:
Hiện tại VIN đang có nhiều project đang là bên thứ 3 sẽ test xong rồi mình sẽ review phân tích, rất nhiều vấn đề cần cover do mismatch thông tin và giai đoạn đó e tự phân tích mấy cái trên nhiều ạ.
Lý do thứ 2 là logic của minitool này có thể tận dụng cho MCFlash: như thêm logic OEM key, thêm logic CRC, có tool tạo file software được từ việc phân tích file software lỗi.
Phân tích được các sequence khi không có requirement cụ thể chỉ từ log can họ gửi -> build sequence cho tool sẽ nhanh hơn.

confirm chút về mini tool 2
User input của tool gồm những thông tin gì nhỉ?
Anh thấy UDS SID Request/Response, sub function types, DID đã được defined sẵn theo các bảng. Vậy cái này có cần user input không
Expeacted Response, Expected Delay After Step (s), P2, P2* value được lấy từ đâu
ST min value được lấy từ Flow Control đúng ko
Result được đánh giá như thế nào?

User input: Request ID, Response ID (có thể input nhiều cặp req vs resp nhưng cần đi theo cặp), Functional ID (kèm theo response ID mới check với), P2, P2*, STmin ạ (cái này với mọi service sẽ cố gắng bắt chính xác với sai số nhỏ, $36 sẽ có mức sai số lớn hơn, cụ thể thì e giờ cũng khó xác định ạ, đến lúc test thử mới chốt được ạ, cái này chỉ để đánh giá timing PASS/FAIL STmin chứ không liên quan đến logic lấy data), channel check ạ. (Có thể cả list service muốn check tại e sợ mình sẽ bắt nhầm response là request, mặc định nếu tester không điền gì thì sẽ là 11, 22, 31, 34, 35, 36, 37, 38, 10, 27, 19, 14, 2E, 28, 85 em sẽ bổ sung sau nếu thấy thiếu ạ)
Em cần lọc với mọi UDS service nhé ạ theo ID input ở trên ạ. (lưu ý là sẽ lọc bỏ Tester Present thôi ạ, và của NRC78 - cái này liên quan đến P2* ạ)
P2, P2* là input của mình ạ. Bỏ qua expected response nhé ạ và delay thì sẽ thay cho em thành khoảng thời gian giữa response lệnh trước với request lệnh sau ạ).
Nếu input ta đưa vào nhỏ STmin trong FC thì sẽ theo STmin trong FC ạ, nếu input ta đưa vào lớn hơn STmin trong FC thì theo input ạ. 
Đánh giá thì hãy tạm thời để cho e đánh giá giai đoạn đầu ạ, nó liên quan đến flashing khá nhiều ạ, rồi e sẽ trao đổi tiếp ạ,
Thêm là mọi người cứ làm với CAN basic trước nhé ạ, xong rồi mới sẽ phát triển CANFD ạ.
Logic là như này ạ: 
Filter channel, filter ID. 
Sau đó ra sẽ check lần lượt từng dòng sau khi lọc, khi bắt đặt Req1 -> Tìm đến Resp1 tương ứng có cùng service với req 1 trong timing là P2 và P2* 
Sau P2/P2* mà không có phản hồi sẽ mặc định coi là timeout ạ. (Timeout vẫn được coi là 1 Resp nhé ạ).
 Từ sau resp1 sẽ lượt lượt check tiếp đến Req2 ạ. Nếu có 1 Req mà gửi giữa Req1 và Resp1 chẳng hạn thì sẽ bỏ qua ạ.
 
 về mini tool 2 bạn cho mình param input và expected results cụ thể để mình test được ko nhỉ?
File test: blf / asc
Input:
ID request & ID response
functional ID & response ID
P2 & P2*
channel

ID request & ID response: Mọi người check trong MC Flash nhé ạ, chỗ ID mapping ạ. + thêm của VCU là (6AC và 62C ạ): tức mọi người nhập 2 bộ giá trị gồm target ECU (check theo ID mapping) và VCU ạ.

Functional ID là 6FF & Response ID thường sẽ nhập chính là response ID ở trên ạ, nhưng không phải lúc nào cũng thế nên chỗ này vẫn yêu cầu tester tự nhập nhé ạ.
P2 = 150ms, P2* = 5000ms ạ. (Theo Diagnostic Spec của VinFast đang sử dụng).
Channel thì e ghi ở tên log can ấy ạ, ko có thì mn cứ mặc định là CAN 1 nhé ạ.
LOG CAN e bổ sung thêm đây nhé ạ.
Expect là e mong muốn có với mỗi dòng là time, request ID, response ID, request data, response data tương ứng (Đã lọc data ạ, kiểu 03 22 F1 90 00 00 00 00 thì in trong report là 22 F1 90 ạ), response time (khoảng thời gian giữa request và response ạ), STmin check result (PASS/FAILED/NA - NA với là với các single frame), delay time (là khoảng thời gian từ response n đến request n+1)
Hi Vinh Tran Hien, mình đang thấy confuse về ID request và response theo ID mapping này
Tức là hiện tại mình đang hiểu là ID request là dạng: 11, 22, 31, 34, 35, 36, 37, 38, 10, 27, 19, 14, 2E, 28, 85
Rồi Respone sẽ react lại về là ID request + 0x40 theo chuẩn UDS
Bạn giải thích chi tiết hơn cho mình clear về workflow nhé

Giá trị của response và request ID là như này ạ
Còn các service mà mình check (11, 22, 31, 34, 35, 36, 37, 38, 10, 27, 19, 14, 2E, 28, 85) thì default mn trước cứ cho e các service này ạ và tester có thể tự bổ sung thay vì code thêm ạ.
Mình bắt request sẽ bao gồm cần đúng cả ID mình nhập và list service mình support ạ.
Response thì mình cũng sẽ ăn theo ID và các trường hợp trả về của service mà request đã detect ạ (gồm positive response (SID + 40), negative response 7F + SID + NRC, và timeout - quá thời gian P2/P2*)

Em có suy nghĩ về STmin mấy hôm nay để sao cho hợp lý ạ, hiện tại ý tưởng của e để check STmin có update như này ạ:
Nguyên nhân là do:
1. Em cần bắt được trường hợp CF trả về nhanh hơn mức STmin cho phép nhưng sai số phần này nhiều khi rất nhỏ để đánh giá hợp lý (ví dụ STmin thường là 0.1ms, nếu CF gửi trong 0.099ms thì vẫn coi như là PASS).
 
2. Trên lý thuyết thì giữa 2 CF cứ lớn hơn STmin và nhỏ hơn mức N_Br là chấp nhận được nhưng nếu chậm quá thì quá trình flash sẽ bị quá chậm -> E cần bắt được log can mà nó chạy với STmin quá lớn. -> Sẽ cần thêm 1 biến để mình giới hạn mức max mình muốn. (thường thì là từ 2ms đã là quá chậm rồi ạ, flash là e chủ yếu là 0.1ms thôi ạ)
 
 -> Sẽ hiển thị kết quả theo % sai số trung bình của request/response và lọc ra được lúc mà STmin trả về quá nhỏ và quá lớn.
Do đó input mới gồm STmin, STmin max value, offset value, force STmin Y/N
Logic sẽ chạy như sau ạ:
+) Check được valid request/response (tức là request và response phải nhận đủ, không timeout ạ) -> Check xem là single frame hay multi frame. -> Single frame thì sẽ đánh NA.
+) Nếu là multiframe thì bắt đầu: Chọn value STmin (cái này e ghi chi tiết ở mục sau STmin Y/N ạ). -> Hiện output là STmin mình sử dụng + STmin trong FC mình detect được.
+) Check STmin từng dòng -> Output gồm có 2 cái: % sai lệch trung bình với giá trị STmin mình nhập vào ạ và  đánh giá PASS/FAIL/PartlyPass.
'PASS là khi tất cả giá trị STmin giữa tất cả các CF > STmin và đều < STmin max value.
'FAIL là khi có ít nhất 1 lần STmin giữa các CF < STmin mình nhập vào đã bao offset value.
'PartlyPass khi tất cả giá trị STmin giữa tất cả các CF > STmin và có ít nhất 1 lần > STmin max value.
Cái force STmin Y/N này thì khá rối:
+) Nếu STmin mình nhập vào < STmin trong FC && Force STmin = N => sử dụng value STmin FC để check. 
+) Nếu STmin mình nhập vào > STmin trong FC && Force STmin =N => sử dụng value STmin mình nhập vào.
+) Nếu Force STmin = Y => sử dụng value STmin minh nhập vào.