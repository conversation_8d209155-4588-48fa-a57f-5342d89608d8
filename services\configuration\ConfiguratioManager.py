import os
from pathlib import Path
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
# Excel support
try:
    import openpyxl
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

class ConfigurationError(Exception):
    """Base exception for configuration operations"""
    pass

class ExcelFormatError(ConfigurationError):
    """Excel-specific format errors"""
    pass

class JSONFormatError(ConfigurationError):
    """JSON-specific format errors"""
    pass

class ConfigurationSchema(ABC):
    """Abstract base class for configuration schemas"""
    
    @abstractmethod
    def get_required_fields(self) -> List[str]:
        """Return list of required fields for this schema"""
        pass
    
    @abstractmethod
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate a single configuration item"""
        pass
    
    @abstractmethod
    def get_configuration_fields(self) -> List[str]:
        """Return Excel column headers for this schema"""
        pass
    
    @abstractmethod
    def config_to_excel_row(self, config: Dict[str, Any], row_index: int) -> List[Any]:
        """Convert configuration to Excel row data"""
        pass
    
    @abstractmethod
    def excel_row_to_config(self, row_data: List[Any]) -> Dict[str, Any]:
        """Convert Excel row data to configuration"""
        pass

class GenericConfigurationHandler(ABC):
    """Base class for configuration file handlers"""
    
    def __init__(self, schema: ConfigurationSchema):
        self.schema = schema
    
    @abstractmethod
    def save(self, data: Dict[str, Any], file_path: str) -> bool:
        """Save configuration data to file"""
        pass
    
    @abstractmethod
    def load(self, file_path: str) -> Dict[str, Any]:
        """Load configuration data from file"""
        pass
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate configuration data structure using schema"""
        if not isinstance(data, dict):
            raise ConfigurationError("Configuration data must be a dictionary")

        if "configurations" not in data:
            raise ConfigurationError("Missing 'configurations' section")

        configurations = data["configurations"]
        if not isinstance(configurations, list):
            raise ConfigurationError("'configurations' must be a list")

        for i, config in enumerate(configurations):
            if not isinstance(config, dict):
                raise ConfigurationError(f"Configuration {i+1} must be a dictionary")
            
            if not self.schema.validate_configuration(config):
                missing_fields = [field for field in self.schema.get_required_fields() 
                                if field not in config]
                if missing_fields:
                    raise ConfigurationError(f"Configuration {i+1} missing required fields: {missing_fields}")

        return True

class JSONConfigHandler(GenericConfigurationHandler):
    """Handler for JSON configuration files"""

    def save(self, data: Dict[str, Any], file_path: str) -> bool:
        """Save configuration data to JSON file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            raise JSONFormatError(f"Failed to save JSON file: {str(e)}")

    def load(self, file_path: str) -> Dict[str, Any]:
        """Load configuration data from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Validate structure
            self.validate_data(data)
            return data

        except json.JSONDecodeError as e:
            raise JSONFormatError(f"Invalid JSON format: {str(e)}")
        except FileNotFoundError:
            raise ConfigurationError(f"File not found: {file_path}")
        except Exception as e:
            raise JSONFormatError(f"Failed to load JSON file: {str(e)}")

class ExcelConfigHandler(GenericConfigurationHandler):
    """Handler for Excel configuration files"""
    def __init__(self, schema: ConfigurationSchema):
        super().__init__(schema)
        if not EXCEL_AVAILABLE:
            raise ImportError("openpyxl library is required for Excel support")

    def save(self, data: Dict[str, Any], file_path: str) -> bool:
        """Save configuration data to Excel file"""
        try:
            wb = Workbook()

            # Create Metadata sheet
            self._create_global_config_sheet(wb, data.get("global_configuration", {}))

            # Create Configurations sheet
            self._create_configurations_sheet(wb, data.get("configurations", []))

            # Remove default sheet if it exists
            if "Sheet" in wb.sheetnames:
                wb.remove(wb["Sheet"])

            wb.save(file_path)
            return True

        except Exception as e:
            raise ExcelFormatError(f"Failed to save Excel file: {str(e)}")

    def load(self, file_path: str) -> Dict[str, Any]:
        """Load configuration data from Excel file"""
        try:
            wb = openpyxl.load_workbook(file_path)

            # Load metadata
            metadata = self._load_metadata_sheet(wb)

            # Load configurations
            configurations = self._load_configurations_sheet(wb)

            return configurations

        except FileNotFoundError:
            raise ConfigurationError(f"File not found: {file_path}")
        except Exception as e:
            raise ExcelFormatError(f"Failed to load Excel file: {str(e)}")

    def _create_configurations_sheet(self, workbook: Workbook, configurations: List[Dict[str, Any]]):
        """Create configurations sheet using schema"""
        ws = workbook.create_sheet("Configurations", 1)

        # Headers from schema
        headers = self.schema.get_configuration_fields()
        for i, header in enumerate(headers, 1):
            ws.cell(row=1, column=i, value=header)
            # Style header
            cell = ws.cell(row=1, column=i)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # Data using schema
        for i, config in enumerate(configurations, 2):
            row_data = self.schema.config_to_excel_row(config, i-1)
            for j, value in enumerate(row_data, 1):
                ws.cell(row=i, column=j, value=value)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

    def _load_configurations_sheet(self, workbook: Workbook) -> Dict[str, Any]:
        """Load configurations using schema"""
        if "Configurations" not in workbook.sheetnames:
            raise ExcelFormatError("Missing 'Configurations' sheet")

        configurations = {"configurations": [], "global_configuration": {}}
        ws = workbook["Configurations"]

        # Read data starting from row 2 (skip header)
        for row in range(2, ws.max_row + 1):
            # Get row data
            row_data = []
            for col in range(1, len(self.schema.get_configuration_fields()) + 1):
                row_data.append(ws.cell(row=row, column=col).value)
            
            # Check if row has data (first data column after row number)
            if not row_data[1]:  # Start address or equivalent first data field
                continue

            config = self.schema.excel_row_to_config(row_data)
            configurations["configurations"].append(config)
        
        ws = workbook["Global Configuration"]
        row_data = []
        for col in range(1, ws.max_column + 1):
            row_data.append(ws.cell(row=2, column=col).value)
            config = self.schema.excel_row_to_global_config(row_data)
            configurations["global_configuration"].update(config)

        return configurations

    # Keep existing metadata methods unchanged for backward compatibility
    def _create_global_config_sheet(self, workbook: Workbook, global_configration: Dict[str, Any]):
        ws = workbook.create_sheet("Global Configuration", 0)
        headers = self.schema.get_global_fields()
        for i, header in enumerate(headers, 1):
            ws.cell(row=1, column=i, value=header)
            # Style header
            cell = ws.cell(row=1, column=i)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        row_data = self.schema.global_config_to_excel_row(global_configration)
        for j, value in enumerate(row_data, 1):
            ws.cell(row=2, column=j, value=value)
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

    def _load_metadata_sheet(self, workbook: Workbook) -> Dict[str, Any]:
        """Load metadata from Excel workbook"""
        if "Metadata" not in workbook.sheetnames:
            return {}

        ws = workbook["Metadata"]
        metadata = {}

        # Read field-value pairs starting from row 2
        for row in range(2, ws.max_row + 1):
            field = ws.cell(row=row, column=1).value
            value = ws.cell(row=row, column=2).value

            if field:
                # Convert field names to match JSON format
                if field == "Created At":
                    metadata["created_at"] = str(value) if value else ""
                elif field == "Version":
                    metadata["version"] = str(value) if value else "1.0"
                elif field == "Total Configurations":
                    metadata["total_configurations"] = int(value) if value else 0

        return metadata

class GenericConfigurationManager:
    """Generic configuration manager supporting multiple schemas and formats"""
    
    def __init__(self, schema: ConfigurationSchema):
        self.schema = schema
        self.json_handler = JSONConfigHandler(schema)
        self.excel_handler = ExcelConfigHandler(schema) if EXCEL_AVAILABLE else None
    
    def detect_file_format(self, file_path: str) -> str:
        """Detect configuration file format based on extension and content"""
        ext = Path(file_path).suffix.lower()

        if ext == '.json':
            return 'json'
        elif ext in ['.xlsx', '.xls']:
            if not EXCEL_AVAILABLE:
                raise ConfigurationError("Excel support not available. Please install openpyxl.")
            return 'excel'
        else:
            # Try to detect by content for files without clear extensions
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)
                return 'json'
            except:
                # If JSON parsing fails, assume it might be Excel
                if EXCEL_AVAILABLE:
                    try:
                        openpyxl.load_workbook(file_path)
                        return 'excel'
                    except:
                        pass
                raise ConfigurationError(f"Unable to determine file format for: {file_path}")

    def save_configuration(self, data: Dict[str, Any], file_path: str, format_type: Optional[str] = None) -> bool:
        """Save configuration data in specified format"""
        if format_type is None:
            format_type = self.detect_file_format(file_path)

        if format_type == 'json':
            return self.json_handler.save(data, file_path)
        elif format_type == 'excel':
            if not self.excel_handler:
                raise ConfigurationError("Excel support not available. Please install openpyxl.")
            return self.excel_handler.save(data, file_path)
        else:
            raise ConfigurationError(f"Unsupported format: {format_type}")

    def load_configuration(self, file_path: str) -> Dict[str, Any]:
        """Load configuration data from file (auto-detect format)"""
        format_type = self.detect_file_format(file_path)

        if format_type == 'json':
            return self.json_handler.load(file_path)
        elif format_type == 'excel':
            if not self.excel_handler:
                raise ConfigurationError("Excel support not available. Please install openpyxl.")
            return self.excel_handler.load(file_path)
        else:
            raise ConfigurationError(f"Unsupported format: {format_type}")

    def get_file_dialog_filter(self) -> str:
        """Get file dialog filter string for supported formats"""
        if EXCEL_AVAILABLE:
            return "Configuration files (*.json *.xlsx *.xls);;JSON files (*.json);;Excel files (*.xlsx *.xls);;All files (*.*)"
        else:
            return "JSON files (*.json);;All files (*.*)"

    def get_save_dialog_filter(self, format_type: str) -> str:
        """Get save dialog filter for specific format"""
        if format_type == 'json':
            return "JSON files (*.json);;All files (*.*)"
        elif format_type == 'excel':
            return "Excel files (*.xlsx);;All files (*.*)"
        else:
            return "All files (*.*)"
