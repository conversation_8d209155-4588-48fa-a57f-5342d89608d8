from services.configuration.ConfiguratioManager import ConfigurationSchema
from typing import Dict, List, Any

class CuttingConfigurationSchema(ConfigurationSchema):
    """Schema for cutting/padding configurations"""
    
    def get_required_fields(self) -> List[str]:
        return ["start_address", "DLC", "isPadding", "padding_value", "format"]
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate cutting configuration"""
        for field in self.get_required_fields():
            if field not in config:
                return False
        return True
    
    def get_configuration_fields(self) -> List[str]:
        return ["Row", "Start Address", "DLC Value", "Padding Enabled", 
                "Padding Value", "Output Format"]

    def get_global_fields(self) -> List[str]:
        return ["Bin Base Address"]
    
    def global_config_to_excel_row(self, config: Dict[str, Any]) -> List[Any]:
        return [
            config.get("bin_base_address", "0x00000000")
        ]
    
    def config_to_excel_row(self, config: Dict[str, Any], row_index: int) -> List[Any]:
        return [
            row_index,
            config.get("start_address", ""),
            config.get("DLC", ""),
            config.get("isPadding", False),
            config.get("padding_value", "0x00"),
            config.get("format", "")
        ]
    
    def excel_row_to_config(self, row_data: List[Any]) -> Dict[str, Any]:
        return {
            "start_address": str(row_data[1]) if row_data[1] else "",
            "DLC": str(row_data[2] or ""),
            "isPadding": bool(row_data[3]),
            "padding_value": str(row_data[4] or "0x00"),
            "format": str(row_data[5] or "") if row_data[5] else None
        }

    def excel_row_to_global_config(self, row_data: List[Any]) -> Dict[str, Any]:
        return {
            "bin_base_address": str(row_data[0]) if row_data[0] else "0x00000000"
        }