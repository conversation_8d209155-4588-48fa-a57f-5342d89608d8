from services.configuration.ConfiguratioManager import ConfigurationSchema
from typing import Dict, List, Any

class UdsAnalysisConfigurationSchema(ConfigurationSchema):
    """Schema for cutting/padding configurations"""
    
    def get_required_fields(self) -> List[str]:
        return ["request_id", "respond_id", "services"]
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate cutting configuration"""
        for field in self.get_required_fields():
            if field not in config:
                return False
        return True
    
    def get_configuration_fields(self) -> List[str]:
        return ["Row", "Request ID", "Response ID", "Services"]

    def get_global_fields(self) -> List[str]:
        return ["Channel", "P2 (ms)", "P2* (ms)", "STmin (ms)", "STmax (ms)", "Output Format"]
    
    def global_config_to_excel_row(self, config: Dict[str, Any]) -> List[Any]:
        return [
            config.get("channel", "CAN 1"),
            config.get("p2_ms", "150"),
            config.get("p2_star_ms", "5000"),
            config.get("stmin_ms", "5000"),
            config.get("stmax_ms", "5000"),
            config.get("output_format", ".xlsx"),
        ]
    
    def config_to_excel_row(self, config: Dict[str, Any], row_index: int) -> List[Any]:
        return [
            row_index,
            config.get("request_id", ""),
            config.get("respond_id", ""),
            config.get("services", "")
        ]
    
    def excel_row_to_config(self, row_data: List[Any]) -> Dict[str, Any]:
        return {
            "request_id": str(row_data[1]) if row_data[1] else "0x000",
            "respond_id": str(row_data[2]) if row_data[2] else "0x000",
            "services": str(row_data[3])
        }

    def excel_row_to_global_config(self, row_data: List[Any]) -> Dict[str, Any]:
        return {
            "channel": str(row_data[0]) if row_data[0] else "CAN 1",
            "p2_ms": str(row_data[1]) if row_data[1] else "150",
            "p2_star_ms": str(row_data[2]) if row_data[2] else "5000",
            "stmin_ms": str(row_data[3]) if row_data[3] else "5000",
            "stmax_ms": str(row_data[4]) if row_data[4] else "5000",
            "output_format": str(row_data[5]) if row_data[5] else ".xlsx"
        }