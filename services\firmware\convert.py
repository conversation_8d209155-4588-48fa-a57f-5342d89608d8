from pathlib import Path
from typing import Optional
from datetime import datetime
import os

from services.firmware.cutting_sw_file import auto_load_segment, do_cut_sw_file
from services.firmware import bincopy
from common import CommonData

def convert_firmware(file_path: str,
                     output_format: Optional[str] = None,
                     output_path: Optional[str] = None,
                     bin_start_addrs: Optional[int] = CommonData.DEFAULT_BIN_START_ADDRESS,
                     ) -> list[str]:
    """
    Convert firmware giữa SREC / Intel HEX / BIN bằng bincopy.

    Args:
        file_path: đường dẫn file đầu vào (.srec/.s19/.s28/.s37/.mot/.hex/.ihex/.bin)
        output_path: đường dẫn file đầu ra (nếu None sẽ tự tạo cùng thư mục với input)
        output_format: đuôi định dạng đầu ra (vd ".bin" hoặc "bin")

    Returns:
        Đường dẫn file đã ghi ra (string).
    """
    # --- validate & normalize ---
    if output_format is None:
        raise ValueError("output_format must NOT be None")
    if not output_format.startswith("."):
        output_format = "." + output_format.lower()
    else:
        output_format = output_format.lower()

    if output_format not in CommonData.SUPPORTED_FIRMWARE_FORMAT:
        raise ValueError(f"Unsupported output format: {output_format}")

    p = Path(file_path)
    if not p.is_file():
        raise FileNotFoundError(f"Input file not found: {file_path}")

    # --- load input ---
    bf = bincopy.BinFile()
    ext = p.suffix.lower()

    if ext in CommonData.SUPPORTED_FIRMWARE_REC_FORMAT:
        bf.add_srec_file(str(p))          # truyền đường dẫn để bincopy tự mở
    elif ext in CommonData.SUPPORTED_FIRMWARE_HEX_FORMAT:
        bf.add_ihex_file(str(p))
    elif ext in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
        bf.add_binary_file(str(p), address=bin_start_addrs)
    else:
        raise ValueError(f"Unsupported firmware extension: {ext}")

    if not bf.segments:
        raise ValueError("Input image has no data")

    # --- decide output path ---
    if output_path is None:
        # cùng thư mục, đổi đuôi theo output_format và thêm timestamp để tránh ghi đè
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = str(p.with_name(f"{p.stem}_converted_{ts}{output_format}"))
    out = Path(output_path)
    out.parent.mkdir(parents=True, exist_ok=True)

    output_paths = []

    # --- write output ---
    if output_format in CommonData.SUPPORTED_FIRMWARE_REC_FORMAT:
        out.write_text(bf.as_srec(), encoding="utf-8")
        output_paths.append(str(out))
    elif output_format in CommonData.SUPPORTED_FIRMWARE_HEX_FORMAT:
        out.write_text(bf.as_ihex(), encoding="utf-8")
        output_paths.append(str(out))
    elif output_format in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
        # Call API : services.firmware 
        plan = auto_load_segment(
            str(file_path),
            output_format=output_format,
            bin_base_address=CommonData.DEFAULT_BIN_START_ADDRESS
        )
        path = do_cut_sw_file(
                str(file_path),
                plan,
                output_dir=None,
                bin_base_address=CommonData.DEFAULT_BIN_START_ADDRESS
            )

        for item in path:
            folder, filename = os.path.split(item)

            new_filename = filename.replace("_cut", "")
            new_p = os.path.join(folder, new_filename)
            print(new_p)

            os.rename(item, new_p)
            output_paths.append(str(new_p))
    return output_paths
