import os
import zlib
from pathlib import Path
from typing import List, Optional, Tuple, Literal
from datetime import datetime
from services.firmware.padding_firmware import padding_firmware
from common import CommonData

try:
    from zoneinfo import ZoneInfo  # Python 3.9+
except Exception:
    ZoneInfo = None  # fallback if not available

from services.firmware.bincopy import BinFile
from models.cut_info import CutInfo

FirmwareFormat = Literal["auto", "ihex", "srec", "bin", "ti-txt", "vmem"]


# ---------- Time / format helpers ----------
def _fmt_time_bkk(now: Optional[datetime] = None) -> str:
    if now is None:
        now = datetime.now()
    return now.strftime("%Y%m%d_%H%M%S")

def _guess_format_from_ext(path: str) -> FirmwareFormat:
    ext = Path(path).suffix.lower()
    if ext in CommonData.SUPPORTED_FIRMWARE_HEX_FORMAT:
        return "ihex"
    if ext in CommonData.SUPPORTED_FIRMWARE_REC_FORMAT:
        return "srec"
    if ext in (".bin",):
        return "bin"
    if ext in (".txt", ".ti-txt", ".titxt"):
        return "ti-txt"
    if ext in (".vmem", ".mem"):
        return "vmem"
    return "auto"

def _crc32_only_data(bf: BinFile) -> int:
    """
    Tính CRC32 chỉ trên dữ liệu thật (concatenate tất cả seg.data),
    bỏ qua mọi khoảng trống (gap) giữa các segment.
    Giả định các segment không chồng lắp. Nếu có chồng lắp, dữ liệu sẽ bị
    tính lặp theo thứ tự xuất hiện trong bf.segments.
    """
    crc = 0
    for seg in sorted(bf.segments, key=lambda s: s.address):
        if seg.data:
            crc = zlib.crc32(seg.data, crc)
    return crc & 0xFFFFFFFF

def _coerce_int(x) -> int:
    """
    Ép 'x' về int. Hỗ trợ:
    - int trực tiếp
    - str dạng '0x...' hoặc thập phân
    - bytes/bytearray chứa số (ít dùng)
    """
    if isinstance(x, int):
        return x
    if isinstance(x, (bytes, bytearray)):
        return int(x)
    if isinstance(x, str):
        x = x.strip()
        base = 16 if x.lower().startswith("0x") else 10
        return int(x, base)
    # fallback (sẽ raise nếu không ép được)
    return int(x)


# ---------- Load / save ----------
def _load_to_binfile(input_file_path: str, *, bin_base_address: Optional[int] = None) -> BinFile:
    if not os.path.exists(input_file_path):
        raise FileNotFoundError(f"Input file '{input_file_path}' does not exist.")
    bf = BinFile()
    fmt = _guess_format_from_ext(input_file_path)
    if fmt == "bin":
        if bin_base_address is None:
            raise ValueError("Binary input requires 'bin_base_address' to map into an address space.")
        bf.add_binary_file(input_file_path, address=bin_base_address)
    elif fmt in ("ihex", "srec", "ti-txt", "auto"):
        bf.add_file(input_file_path)  # auto-detect textual formats
    elif fmt == "vmem":
        bf.add_verilog_vmem_file(input_file_path)
    else:
        raise ValueError(f"Unsupported input format for '{input_file_path}'.")
    return bf

def _write_binfile(bf: BinFile, output_path: Path, out_fmt: Optional[FirmwareFormat] = None) -> None:
    out_fmt = (out_fmt or _guess_format_from_ext(str(output_path))).lower()
    if out_fmt == "bin":
        output_path.write_bytes(bf.as_binary())
    elif out_fmt == "srec":
        output_path.write_text(bf.as_srec(), newline="\n")
    elif out_fmt == "ti-txt":
        output_path.write_text(bf.as_ti_txt(), newline="\n")
    elif out_fmt == "vmem":
        output_path.write_text(bf.as_verilog_vmem(), newline="\n")
    else:  # default to Intel-HEX
        output_path.write_text(bf.as_ihex(), newline="\n")

# ---------- Range utils ----------
def _merged_ranges_from_segments(bf: BinFile) -> List[Tuple[int, int]]:
    segs: List[Tuple[int, int]] = []
    for seg in bf.segments:
        s, e = seg.address, seg.address + len(seg.data)
        if e > s:
            segs.append((s, e))
    if not segs:
        return []
    segs.sort(key=lambda x: x[0])

    merged: List[Tuple[int, int]] = []
    cs, ce = segs[0]
    for s, e in segs[1:]:
        if s <= ce:  # overlap or touching
            ce = max(ce, e)
        else:
            merged.append((cs, ce))
            cs, ce = s, e
    merged.append((cs, ce))
    return merged

def _flat_bytes_with_padding(bf: BinFile, start: int, end: int, pad_byte: int = 0xFF) -> bytes:
    L = end - start
    out = bytearray([pad_byte]) * L
    for seg in bf.segments:
        ss, se = seg.address, seg.address + len(seg.data)
        if se <= start or ss >= end:
            continue
        xs, xe = max(ss, start), min(se, end)
        out[xs-start:xe-start] = seg.data[xs-ss:xe-ss]
    return bytes(out)

def _slice_to_binfile(bf: BinFile, start: int, end: int) -> BinFile:
    dst = BinFile()
    for seg in bf.segments:
        ss, se = seg.address, seg.address + len(seg.data)
        if se <= start or ss >= end:
            continue
        xs, xe = max(ss, start), min(se, end)
        chunk = seg.data[xs-ss:xe-ss]
        if chunk:
            dst.add_binary(bytes(chunk), xs)
    return dst

def _create_output_path(input: Path, start: int, dlc: int, out_format: str, padding_value: Optional[int] = None):
    out_dir = input.parent
    out_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if not padding_value is None:
        filename = f"{input.stem}_cut_0x{start:08X}_0x{dlc:08X}_padded_0x{padding_value:02X}_{timestamp}{out_format}"
    else:
        filename = f"{input.stem}_cut_0x{start:08X}_0x{dlc:08X}_{timestamp}{out_format}"

    return out_dir / filename

# =============== API 1: auto_load_segment =================
def auto_load_segment(
    input_file_path: str,
    *,
    output_format: Optional[str] = None,
    bin_base_address: Optional[int] = None,
    pad_byte_for_crc: int = 0xFF,
    note: Optional[str] = None,
    timestamp: Optional[str] = None,
    print_rows: bool = True,
    write_index_csv: bool = False,
    index_csv_dir: Optional[str] = None,
    show_input_info: bool = False,
) -> List[CutInfo]:
    """
    Phân tích file, đưa ra *kế hoạch cắt* (cut plan) nhưng KHÔNG tạo file.
    Trả về list CutInfo với filename (đã gồm timestamp), start_address, DLC, CRC32, Note.

    - output_format: định dạng dùng để đặt *đuôi file* trong filename của kế hoạch (không ghi file).
                     Nếu None -> suy theo đuôi input.
    - show_input_info: nếu True, chèn thêm dòng đầu tiên mô tả toàn bộ input:
        filename = tên file gốc
        start_address = start của range đầu tiên
        DLC = (start cuối + dlc cuối) - start đầu  (độ phủ toàn file, tính gồm gaps)
        CRC32 = CRC32 tính trên toàn vùng phủ (đã pad = pad_byte_for_crc)
        Note = "start_last - start_first + dlc_last" (ở dạng hex)
    """
    in_path = Path(input_file_path)
    bf = _load_to_binfile(str(in_path), bin_base_address=bin_base_address)
    ranges = _merged_ranges_from_segments(bf)
    if not ranges:
        raise ValueError("No data segments found in the input file.")

    ts = timestamp or _fmt_time_bkk()
    base_stem = in_path.stem
    out_ext = in_path.suffix.lower() if output_format is None else output_format
    note_str = note if note is not None else f"Auto-split from {in_path.name} @ {ts}"

    plan: List[CutInfo] = []
    for idx, (start, end) in enumerate(ranges, start=1):
        dlc = end - start
        flat = _flat_bytes_with_padding(bf, start, end, pad_byte=pad_byte_for_crc)
        crc = zlib.crc32(flat) & 0xFFFFFFFF
        filename = f"{base_stem}_cut_No{idx}_{ts}{out_ext}"
        plan.append(CutInfo(filename=filename, start_address=start, dlc=dlc, crc32=crc, note=note_str))

    # === NEW: chèn dòng thông tin input ở vị trí đầu nếu được yêu cầu ===
    if show_input_info:
        start_first = ranges[0][0]
        start_last, end_last = ranges[-1]
        dlc_last = end_last - start_last
        coverage_start = start_first

        # CRC32 chỉ trên dữ liệu thật (không pad)
        crc_all = _crc32_only_data(bf)

        # DLC của dòng input = TỔNG DLC các dải sẽ cắt (tổng byte dữ liệu thật)
        total_dlc_data = sum((e - s) for (s, e) in ranges)

        note_value = (start_last - start_first + dlc_last)
        note_input = f"Total size : 0x{note_value:X}"

        input_info_row = CutInfo(
            filename=in_path.name,         # giữ nguyên tên file gốc và đuôi hiện tại
            start_address=coverage_start,  # start đầu tiên
            dlc=total_dlc_data,              # DLC của toàn file (độ phủ)
            crc32=crc_all,                 # CRC của toàn file (độ phủ, đã pad)
            note=note_input,               # theo format yêu cầu
        )
        plan.insert(0, input_info_row)

    if print_rows:
        for r in plan:
            print(r.as_row())

    if write_index_csv:
        csv_dir = Path(index_csv_dir) if index_csv_dir else in_path.parent
        csv_dir.mkdir(parents=True, exist_ok=True)
        idx_csv = csv_dir / f"{base_stem}_cut_index_{ts}.csv"
        with idx_csv.open("w", newline="\n", encoding="utf-8") as f:
            f.write("filename,start_address,DLC,CRC32,Note\n")
            for r in plan:
                f.write(f"{r.filename},0x{r.start_address:08X},0x{r.dlc:X},0x{r.crc32:08X},{r.note}\n")
        print(f"Index saved: {idx_csv}")

    return plan

# =============== API 2: do_cut_sw_file =================
def do_cut_sw_file(
    input_file_path: str,
    cut_plan: List[CutInfo],
    *,
    output_dir: Optional[str] = None,
    bin_base_address: Optional[int] = None,
) -> List[Path]:
    """
    Thực thi cắt file dựa trên cut_plan đã tạo bởi auto_load_segment().
    - Giữ filename trong cut_plan (kể cả đuôi -> format). Không đổi tên/định dạng.
    - output_dir: thư mục ghi file (mặc định cạnh input).
    Trả về danh sách Path các file đã ghi.
    """
    in_path = Path(input_file_path)
    out_dir = Path(output_dir) if output_dir else in_path.parent
    out_dir.mkdir(parents=True, exist_ok=True)

    bf = _load_to_binfile(str(in_path), bin_base_address=bin_base_address)
    written: List[Path] = []

    for item in cut_plan:
        start = item.start_address
        end = item.start_address + item.dlc
        dst = _slice_to_binfile(bf, start, end)

        out_path = out_dir / item.filename
        # format sẽ được suy theo đuôi trong item.filename
        _write_binfile(dst, out_path)
        written.append(out_path)
        print(f"Wrote: {out_path}")

    return written

# =============== API 3: custom_load_segment =================
def custom_load_segment(
    input_file_path: str,
    segments: List[Tuple[int, int]] | List[dict],
    *,
    output_format: Optional[str] = None,
    bin_base_address: Optional[int] = None,
    pad_byte_for_crc: int = 0xFF,
    note: Optional[str] = None,
    timestamp: Optional[str] = None,
    print_rows: bool = True,
    write_index_csv: bool = False,
    index_csv_dir: Optional[str] = None,
    keep_input_order: bool = True,
) -> List[CutInfo]:
    """
    Tạo kế hoạch cắt (cut plan) theo danh sách 'segments' do người dùng chỉ định.
    KHÔNG ghi file; trả về list CutInfo (filename, start_address, DLC, CRC32, Note).

    Args:
        input_file_path: đường dẫn file firmware gốc (.hex/.srec/.bin/...)
        segments: danh sách các mục cần cắt.
            - Dạng được hỗ trợ:
              + List[Tuple[int, int]]  => [(start, dlc), ...]
              + List[dict]             => [{"start_address":..., "dlc":...}, ...]
            - start/dlc có thể là int hoặc str ('0x...' hoặc thập phân)
        output_format: dùng để quy định phần đuôi filename trong plan (không ghi file).
                       Nếu None -> suy theo đuôi input.
        bin_base_address: base address khi input là .bin.
        pad_byte_for_crc: byte fill dùng để tính CRC32 trên toàn DLC (default 0xFF).
        note: ghi chú cho mọi mục (default: "Custom-split from <file> @ <ts>").
        timestamp: chuỗi thời gian để cố định tên file (nếu None -> tự sinh theo Asia/Bangkok).
        print_rows: nếu True -> in từng dòng như CSV ra console.
        write_index_csv: nếu True -> ghi một CSV chứa metadata của plan.
        index_csv_dir: thư mục đặt CSV (mặc định cạnh file input).
        keep_input_order: nếu True -> giữ nguyên thứ tự input; nếu False -> sort theo start_address.

    Returns:
        List[CutInfo]
    """
    in_path = Path(input_file_path)
    bf = _load_to_binfile(str(in_path), bin_base_address=bin_base_address)

    # Chuẩn hoá input segments -> [(start:int, dlc:int), ...]
    norm: List[Tuple[int, int]] = []
    if isinstance(segments, list):
        for item in segments:
            if isinstance(item, dict):
                s = _coerce_int(item.get("start_address"))
                d = _coerce_int(item.get("dlc"))
            elif isinstance(item, (tuple, list)) and len(item) == 2:
                s = _coerce_int(item[0])
                d = _coerce_int(item[1])
            else:
                raise ValueError("Each segment must be (start_address, dlc) or {'start_address':..., 'dlc':...}.")
            if d <= 0:
                raise ValueError(f"DLC must be > 0. Got {d} for start 0x{s:08X}.")
            norm.append((s, d))
    else:
        raise ValueError("segments must be a list.")

    if not keep_input_order:
        norm.sort(key=lambda t: t[0])  # sắp xếp theo start_address

    # Thông tin định dạng & timestamp
    ts = timestamp or _fmt_time_bkk()
    base_stem = in_path.stem
    out_ext = in_path.suffix.lower() if output_format is None else output_format
    note_str = note if note is not None else f"Custom-split from {in_path.name} @ {ts}"

    # Tạo plan
    plan: List[CutInfo] = []
    for idx, (start, dlc) in enumerate(norm, start=1):
        end = start + dlc
        flat = _flat_bytes_with_padding(bf, start, end, pad_byte=pad_byte_for_crc)
        crc = zlib.crc32(flat) & 0xFFFFFFFF
        filename = f"{base_stem}_cut_No{idx}_{ts}{out_ext}"
        plan.append(CutInfo(filename=filename, start_address=start, dlc=dlc, crc32=crc, note=note_str))

    if print_rows:
        for r in plan:
            print(r.as_row())

    if write_index_csv:
        csv_dir = Path(index_csv_dir) if index_csv_dir else in_path.parent
        csv_dir.mkdir(parents=True, exist_ok=True)
        idx_csv = csv_dir / f"{base_stem}_custom_cut_index_{ts}.csv"
        with idx_csv.open("w", newline="\n", encoding="utf-8") as f:
            f.write("filename,start_address,DLC,CRC32,Note\n")
            for r in plan:
                f.write(f"{r.filename},0x{r.start_address:08X},0x{r.dlc:X},0x{r.crc32:08X},{r.note}\n")
        print(f"Index saved: {idx_csv}")

    return plan

# =============== API 4: do_cut_sw_file_with_format =================
def do_cut_sw_file_with_format(
    input_file_path: str,
    start_address: int,
    dlc: int,
    format: Optional[str] = None,
    isPadding: bool = False,
    padding_value: Optional[int] = None,
    bin_base_address: Optional[int] = None,
) -> Path:
    """
    Thực thi cắt file dựa trên input.
    - Cắt file base address và DLC từ user
    - Đổi định dạng file theo yêu cầu
    - output_dir: thư mục ghi file (mặc định cạnh input).
    Trả về Path các file đã ghi.
    """
    in_path = Path(input_file_path)
    in_format = in_path.suffix.lower()

    out_format = in_format if format == None else format

    start = start_address
    end = start_address + dlc

    bf = _load_to_binfile(str(in_path), bin_base_address=bin_base_address)

    output_paths = []
    if start + dlc < bf.minimum_address:
        if isPadding:
            tmp_bf = BinFile()
            tmp_bf.add_binary(bytes([padding_value]) * dlc, address=start)
            output_path = _create_output_path(in_path, start, dlc, out_format, padding_value)
            output_paths.append(output_path)
            _write_binfile(tmp_bf, output_path, None)
        else:
            raise ValueError(f"start_addr(0x{start:08X} + dlc(0x{dlc:08X}) < min_addr(0x{bf.minimum_address:08X}))")

    elif start < bf.minimum_address:
        if isPadding:
            bf.add_binary(bytes([padding_value]) * (bf.minimum_address - start), address=start)
            bf.crop(start, end)
            bf.fill(bytes([padding_value]))
            output_path = _create_output_path(in_path, start, dlc, out_format, padding_value)
            output_paths.append(output_path)
            _write_binfile(bf, output_path, None)
        else:
            bf.crop(bf.minimum_address, end)
            if out_format in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
                for seg in bf.segments:
                    bin_seg_bf = BinFile()
                    bin_start = seg.minimum_address
                    bin_dlc = len(seg.data)
                    bin_seg_bf.add_binary(bytes(seg.data), bin_start)

                    output_path = _create_output_path(in_path, bin_start, bin_dlc, out_format)
                    output_paths.append(output_path)
                    _write_binfile(bin_seg_bf, output_path, None)
            else:
                output_path = _create_output_path(in_path, bf.minimum_address,
                                    (end - bf.minimum_address), out_format)
                output_paths.append(output_path)
                _write_binfile(bf, output_path, None)

    elif start + dlc < bf.maximum_address:
        bf.crop(start, end)
        if isPadding:
            bf.fill(bytes([padding_value]))
            output_path = _create_output_path(in_path, start, dlc, out_format, padding_value)
            output_paths.append(output_path)
            _write_binfile(bf, output_path, None)
        else:
            if out_format in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
                for seg in bf.segments:
                    bin_seg_bf = BinFile()
                    bin_start = seg.minimum_address
                    bin_dlc = len(seg.data)
                    bin_seg_bf.add_binary(bytes(seg.data), bin_start)

                    output_path = _create_output_path(in_path, bin_start, bin_dlc, out_format)
                    output_paths.append(output_path)
                    _write_binfile(bin_seg_bf, output_path, None)
            else:
                output_path = _create_output_path(in_path, start, dlc, out_format)
                output_paths.append(output_path)
                _write_binfile(bf, output_path, None)

    elif start < bf.maximum_address:
        if isPadding:
            bf.add_binary(bytes([padding_value]) * (end - bf.maximum_address), address=bf.maximum_address)
            bf.crop(start, end)
            bf.fill(bytes([padding_value]))
            output_path = _create_output_path(in_path, start, dlc, out_format, padding_value)
            output_paths.append(output_path)
            _write_binfile(bf, output_path, None)
        else:
            bf.crop(start, bf.maximum_address)
            if out_format in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
                for seg in bf.segments:
                    bin_seg_bf = BinFile()
                    bin_start = seg.minimum_address
                    bin_dlc = len(seg.data)
                    bin_seg_bf.add_binary(bytes(seg.data), bin_start)

                    output_path = _create_output_path(in_path, bin_start, bin_dlc, out_format)
                    output_paths.append(output_path)
                    _write_binfile(bin_seg_bf, output_path, None)
            else:
                output_path = _create_output_path(in_path, start,
                                    (bf.maximum_address - start),
                                    out_format)
                output_paths.append(output_path)
                _write_binfile(bf, output_path, None)
    else:
        if isPadding:
            tmp_bf = BinFile()
            tmp_bf.add_binary(bytes([padding_value]) * dlc, address=start)
            output_path = _create_output_path(in_path, start, dlc, out_format, padding_value)
            output_paths.append(output_path)
            _write_binfile(tmp_bf, output_path, None)
        else:
            raise ValueError(f"start_addr(0x{start:08X} > max_addr(0x{bf.maximum_address:08X}))")

    for out in output_paths:
        print(f"Wrote: {out}")

    return output_paths
