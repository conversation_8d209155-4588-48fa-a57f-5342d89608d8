from pathlib import Path
from typing import Optional
from datetime import datetime
from services.firmware import bincopy
from common import CommonData

def padding_firmware(file_path: str, padding_value: int = 0x00,
                    output_path: Optional[str] = None,
                    output_format: Optional[str] = None,
                    dlc: Optional[int] = None) -> str:
    """ Load a firmware file (SREC / Intel HEX / BIN) with bincopy and output a
        padded/truncated file with the same format as input.

    Args:
        file_path (str): Input firmware file.
        padding_value (int): Padding byte value (0..255).
        output_path (str): Output firmware file path.
        output_format (str): Output firmware format.
        dlc(int): Data length count of output file.

    Raises:
        ValueError: Invalid input
        FileNotFoundError: file_path not found

    Returns:
        str: The path to the written padded firmware file.
    """

    p = Path(file_path)
    if not p.is_file():
        raise FileNotFoundError(f"Input file not found: {file_path}")

    # Load with bincopy
    bf = bincopy.BinFile()
    ext = p.suffix.lower()

    if ext in CommonData.SUPPORTED_FIRMWARE_REC_FORMAT:
        bf.add_srec_file(str(p))
    elif ext in CommonData.SUPPORTED_FIRMWARE_HEX_FORMAT:
        bf.add_ihex_file(str(p))
    elif ext in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
        bf.add_binary_file(str(p), address=0)
    else:
        raise ValueError(f"Unsupported firmware extension: {ext}")

    if not bf.segments:
        raise ValueError("Input image has no data.")

    if not (0 <= padding_value <= 0xFF):
        raise ValueError("padding_value must be between 0 and 255.")

    if output_format == None:
        output_format = ext
    elif not output_format in CommonData.SUPPORTED_FIRMWARE_FORMAT:
        raise ValueError(f"Unsupported output format {output_format}")

    # Use bincopy built-in padding
    start = bf.minimum_address
    blob = bf.as_binary(padding=bytes([padding_value]))

    # Handle case custom cut with maximun_address > original file maximum address
    if not dlc is None and (start + dlc) > bf.maximum_address:
        blob += bytes([padding_value]) * ((start + dlc) - bf.maximum_address)

    # Create a new BinFile with contiguous padded data
    out_bf = bincopy.BinFile()
    out_bf.add_binary(blob, address=start)

    if output_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path =\
            (str(p.with_name(f"{p.stem}_padded_0x{padding_value:08X}_{timestamp}{output_format}")))

    # Save in same format as input
    if output_format in CommonData.SUPPORTED_FIRMWARE_REC_FORMAT:
        Path(output_path).write_text(out_bf.as_srec())
    elif output_format in CommonData.SUPPORTED_FIRMWARE_HEX_FORMAT:
        Path(output_path).write_text(out_bf.as_ihex())
    elif output_format in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT:
        Path(output_path).write_bytes(blob)

    print(f"Wrote: {output_path}")
    return output_path
