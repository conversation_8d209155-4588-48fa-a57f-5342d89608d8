import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QPushButton, 
                              QProgressDialog, QVBoxLayout, QWidget, QLabel)
from PyQt6.QtCore import QObject, pyqtSignal, QThread, pyqtSlot, QMutex, QMutexLocker, QTimer, QEventLoop


# ============================================================================
# MODEL: Handles the actual analysis work
# ============================================================================
class AnalysisModel(QObject):
    """
    This is the 'real model' that performs analysis.
    It runs in a separate thread to avoid blocking the UI.
    """
    
    # SIGNALS emitted by the model
    progress_updated = pyqtSignal(int)      # Emits progress percentage (0-100)
    analysis_completed = pyqtSignal(str)    # Emits result when done
    analysis_cancelled = pyqtSignal()       # Emits when cancelled
    
    def __init__(self):
        super().__init__()
        self._is_cancelled = False
        self._mutex = QMutex()  # Thread-safe access to _is_cancelled
    
    def _sleep_with_events(self, milliseconds):
        """
        Sleep while processing events - this allows signals to be received!
        """
        loop = QEventLoop()
        QTimer.singleShot(milliseconds, loop.quit)
        loop.exec()
    
    @pyqtSlot()  # SLOT: receives signal from UI to start analysis
    def start_analysis(self):
        """
        This method is called when the 'analysis_requested' signal is received.
        It performs the analysis and emits progress updates.
        """
        print("📊 Model: Starting analysis...")
        with QMutexLocker(self._mutex):
            self._is_cancelled = False
        
        # Simulate a long-running analysis process
        for i in range(101):
            # Check cancellation BEFORE doing work (thread-safe)
            with QMutexLocker(self._mutex):
                if self._is_cancelled:
                    print("❌ Model: Analysis cancelled!")
                    self.analysis_cancelled.emit()
                    return
            
            # Emit progress update
            self.progress_updated.emit(i)
            print(f"📈 Model: Progress {i}%")
            
            # Sleep while allowing event processing
            # Break into smaller chunks for more responsive cancellation
            for j in range(5):
                with QMutexLocker(self._mutex):
                    if self._is_cancelled:
                        print("❌ Model: Analysis cancelled during work!")
                        self.analysis_cancelled.emit()
                        return
                # This sleep processes events, allowing cancel_analysis() to be called!
                self._sleep_with_events(10)  # 10ms sleep with event processing
        
        # Analysis complete
        result = "Analysis completed successfully!"
        print(f"✅ Model: {result}")
        self.analysis_completed.emit(result)
    
    @pyqtSlot()  # SLOT: receives signal from UI to cancel
    def cancel_analysis(self):
        """
        This method is called when the 'cancel_requested' signal is received.
        """
        with QMutexLocker(self._mutex):
            self._is_cancelled = True


# ============================================================================
# VIEW: The main window with the "Analysis" button
# ============================================================================
class MainWindow(QMainWindow):
    """
    The main UI window that contains the Analysis button.
    """
    
    # SIGNALS emitted by the view
    analysis_requested = pyqtSignal()  # Emitted when button is clicked
    cancel_requested = pyqtSignal()    # Emitted when progress dialog is cancelled
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Signal & Slot Example")
        self.setGeometry(100, 100, 400, 200)
        
        # Create UI components
        self.setup_ui()
        
        # Create the model and worker thread
        self.setup_model()
        
        # Connect signals and slots
        self.connect_signals()
    
    def setup_ui(self):
        """Create the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Label
        label = QLabel("Click the button to start analysis")
        label.setStyleSheet("font-size: 14px; margin: 20px;")
        layout.addWidget(label)
        
        # Analysis button
        self.analysis_button = QPushButton("Analysis")
        self.analysis_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 16px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.analysis_button.clicked.connect(self.on_analysis_clicked)
        layout.addWidget(self.analysis_button)
        
        # Result label
        self.result_label = QLabel("")
        self.result_label.setStyleSheet("font-size: 12px; color: #666; margin: 20px;")
        self.result_label.setWordWrap(True)
        layout.addWidget(self.result_label)
        
        # Progress dialog (created but not shown yet)
        self.progress_dialog = None
    
    def setup_model(self):
        """Create the analysis model and move it to a worker thread"""
        # Create a separate thread for the model
        self.worker_thread = QThread()
        
        # Create the model
        self.model = AnalysisModel()
        
        # Move model to the worker thread
        self.model.moveToThread(self.worker_thread)
        
        # Start the thread
        self.worker_thread.start()
    
    def connect_signals(self):
        """
        Connect all signals and slots.
        This is where the magic happens!
        """
        # VIEW -> MODEL: When analysis is requested, start the model's analysis
        self.analysis_requested.connect(self.model.start_analysis)
        
        # VIEW -> MODEL: When cancel is requested, cancel the model's analysis
        self.cancel_requested.connect(self.model.cancel_analysis)
        
        # MODEL -> VIEW: When model reports progress, update the progress dialog
        self.model.progress_updated.connect(self.update_progress)
        
        # MODEL -> VIEW: When model completes, show the result
        self.model.analysis_completed.connect(self.on_analysis_completed)
        
        # MODEL -> VIEW: When model is cancelled, close progress dialog
        self.model.analysis_cancelled.connect(self.on_analysis_cancelled)
    
    @pyqtSlot()  # SLOT: called when analysis button is clicked
    def on_analysis_clicked(self):
        """Handle the Analysis button click"""
        print("🖱️  View: Analysis button clicked")
        
        # Disable button during analysis
        self.analysis_button.setEnabled(False)
        self.result_label.setText("")
        
        # Create and show progress dialog
        self.progress_dialog = QProgressDialog(
            "Analyzing data...", "Cancel", 0, 100, self
        )
        self.progress_dialog.setWindowTitle("Analysis Progress")
        self.progress_dialog.setMinimumDuration(0)  # Show immediately
        
        # Connect progress dialog's cancel button to emit our signal
        self.progress_dialog.canceled.connect(self.on_cancel_clicked)
        
        self.progress_dialog.show()
        
        # EMIT SIGNAL to request analysis from model
        print("📤 View: Emitting 'analysis_requested' signal")
        self.analysis_requested.emit()
    
    @pyqtSlot()  # SLOT: called when progress dialog is cancelled
    def on_cancel_clicked(self):
        """Handle the Cancel button in progress dialog"""
        print("🖱️  View: Cancel button clicked in progress dialog")
        
        # EMIT SIGNAL to request cancellation
        print("📤 View: Emitting 'cancel_requested' signal")
        self.cancel_requested.emit()
    
    @pyqtSlot(int)  # SLOT: receives progress updates from model
    def update_progress(self, value):
        """Update the progress dialog"""
        if self.progress_dialog:
            self.progress_dialog.setValue(value)
    
    @pyqtSlot(str)  # SLOT: receives completion message from model
    def on_analysis_completed(self, result):
        """Handle analysis completion"""
        print(f"📬 View: Received completion signal with result: {result}")
        
        # Close progress dialog
        if self.progress_dialog:
            self.progress_dialog.close()
        
        # Show result
        self.result_label.setText(f"✅ {result}")
        self.result_label.setStyleSheet("font-size: 12px; color: green; margin: 20px;")
        
        # Re-enable button
        self.analysis_button.setEnabled(True)
    
    @pyqtSlot()  # SLOT: receives cancellation confirmation from model
    def on_analysis_cancelled(self):
        """Handle analysis cancellation"""
        print("📬 View: Received cancellation signal")
        
        # Close progress dialog
        if self.progress_dialog:
            self.progress_dialog.close()
        
        # Show cancellation message
        self.result_label.setText("❌ Analysis was cancelled")
        self.result_label.setStyleSheet("font-size: 12px; color: red; margin: 20px;")
        
        # Re-enable button
        self.analysis_button.setEnabled(True)
    
    def closeEvent(self, event):
        """Clean up when window is closed"""
        self.worker_thread.quit()
        self.worker_thread.wait()
        event.accept()


# ============================================================================
# MAIN APPLICATION
# ============================================================================
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())