# tests/test_cutting_service.py
import os
import unittest
from pathlib import Path

from services.firmware.cutting_sw_file import auto_load_segment, do_cut_sw_file, custom_load_segment

ROOT = Path(__file__).resolve().parents[1]
INPUT = ROOT / "test_input.hex"     # dùng file thật của bạn nếu đã có
OUTDIR = ROOT / "out_tests"
OUTDIR1 = ROOT / "out_tests_custom"

# Fallback sample nếu bạn CHƯA có test_input.hex ở root
SAMPLE_HEX = """\
:020000040000FA
:100000000C945C000C946E000C946E000C946E00CA
:100010000C946E000C946E000C946E000C946E00A8
:00000001FF
"""

segments = [
    ("0x80080000", "0x3800"),
    ("0x800C0000", "0x100"),
    ("0x800D0000", "0xA9900"),
    ("0x8021FE00", "0x38200"),
    ("0x80270000", "0x4100"),
]

def ensure_sample_input():
    if not INPUT.exists():
        INPUT.write_text(SAMPLE_HEX, newline="\n")

class TestCuttingService(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        ensure_sample_input()
        OUTDIR.mkdir(exist_ok=True)

    # uncomment if don't need output files
    # def tearDown(self):
    #     # dọn file sinh ra mỗi test
    #     for p in OUTDIR.glob("*"):
    #         try:
    #             p.unlink()
    #         except Exception:
    #             pass

    def test_auto_load_segment(self):
        plan = auto_load_segment(
            str(INPUT),
            output_format=None,    # giữ theo đuôi input (.hex) để đặt tên file
            pad_byte_for_crc=0xFF,
            print_rows=True,      # không in ra console trong unit test
            write_index_csv=False  # không ghi CSV trong unit test
        )
        self.assertGreaterEqual(len(plan), 1)
        for item in plan:
            self.assertTrue(item.filename)              # có tên file
            self.assertTrue(item.dlc > 0)               # DLC > 0
            self.assertIsInstance(item.crc32, int)      # CRC32 là int
            self.assertTrue(item.note)                  # có note

    def test_do_cut_sw_file(self):
        # tạo plan trước
        plan = auto_load_segment(
            str(INPUT),
            output_format=None,  # ép ra .hex cho dễ kiểm tra
            print_rows=False,
            write_index_csv=False
        )
        # cắt theo plan
        written = do_cut_sw_file(
            str(INPUT),
            plan,
            output_dir=str(OUTDIR)
        )
        self.assertGreaterEqual(len(written), 1)
        # kiểm tra file tồn tại, có dung lượng > 0
        for p in written:
            self.assertTrue(p.exists(), f"not found: {p}")
            self.assertGreater(p.stat().st_size, 0, f"empty file: {p}")
            # tùy chọn: check đuôi .hex
            # self.assertEqual(p.suffix.lower(), ".hex")

    def test_custom_load_and_cut(self):
        # tạo input mẫu nhỏ (hoặc dùng file thật của bạn)
        plan = custom_load_segment(
            str(INPUT),
            segments,
            output_format=".s19",     # hoặc "bin"/"srec"/"ti-txt"/"vmem"/None
            pad_byte_for_crc=0xFF,
            print_rows=True,          # in từng dòng như CSV
            write_index_csv=False      # nếu muốn có file CSV metadata
        )

        # cắt theo plan
        written = do_cut_sw_file(
            str(INPUT),
            plan,
            output_dir=str(OUTDIR1)
        )
        self.assertGreaterEqual(len(written), 1)
        # kiểm tra file tồn tại, có dung lượng > 0
        for p in written:
            self.assertTrue(p.exists(), f"not found: {p}")
            self.assertGreater(p.stat().st_size, 0, f"empty file: {p}")
            # tùy chọn: check đuôi .hex
            # self.assertEqual(p.suffix.lower(), ".hex")


if __name__ == "__main__":
    unittest.main()
