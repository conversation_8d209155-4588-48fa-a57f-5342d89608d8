from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QLabel)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class CrcCalculationView(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Placeholder content for CRC Checking tab
        label = QLabel("CRC Checking Tab")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setFont(QFont("Arial", 16))

        layout.addWidget(label)
        self.setLayout(layout)
