import os
from datetime import datetime
from pathlib import Path
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QHeaderView, QCheckBox, QFileDialog, QMessageBox, QComboBox, QSplitter, QTableView, QLabel, QAbstractItemView, QInputDialog)
from PyQt6.QtCore import (Qt, QSettings, QRegularExpression)
from PyQt6.QtGui import (QStandardItemModel, QStandardItem, QRegularExpressionValidator)
from common import CommonData

from services.firmware.cutting_sw_file import auto_load_segment, do_cut_sw_file, do_cut_sw_file_with_format
from services.firmware.convert import convert_firmware
from services.firmware.padding_firmware import padding_firmware
from services.configuration.ConfiguratioManager import GenericConfigurationManager
from services.configuration.CuttingConfigurationSchema import CuttingConfigurationSchema


class CuttingPaddingView(QWidget):
    def __init__(self):
        super().__init__()
        self.config_manager = GenericConfigurationManager(CuttingConfigurationSchema())
        self.init_ui()

    def init_ui(self):
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        self.splitter = QSplitter(parent=self)
        self.splitter.setOrientation(Qt.Orientation.Vertical)
        self.splitter.setHandleWidth(5)
        self.splitter.setStyleSheet("""
        QSplitter::handle {
            background-color: #888;
        }
        QSplitter::handle:hover {
            background-color: #555;
        }
        """)

        self.init_default_panel_ui()
        self.init_cutting_panel_ui()

        self.splitter.setSizes([1, 1])

        self.main_layout.addWidget(self.splitter)
        self.setLayout(self.main_layout)

    def init_default_panel_ui(self):
        self.default_panel = QWidget(parent=self)
        self.default_panel_layout = QVBoxLayout(self.default_panel)

        default_panel_label = QLabel("Default Configuration")
        default_panel_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        self.default_panel_layout.addWidget(default_panel_label)

        self.init_default_toolbar()
        self.init_default_table()

        self.splitter.addWidget(self.default_panel)

    def init_default_toolbar(self):
        self.default_toolbar = QWidget(self.default_panel)
        self.default_toolbar_layout = QHBoxLayout(self.default_toolbar)

        # Software file input
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("path/to/software file")
        self.file_path_input.setMinimumHeight(30)
        self.file_path_input.setReadOnly(True)
        self.file_path_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        self.default_toolbar_layout.addWidget(self.file_path_input, 5)

        self.software_file_btn = QPushButton("📁 Software file")
        self.software_file_btn.setMinimumHeight(30)
        self.software_file_btn.setStyleSheet(self.get_button_style())
        self.default_toolbar_layout.addWidget(self.software_file_btn, 1)
        self.software_file_btn.clicked.connect(self.select_software_file)

        self.padding_value_input = QLineEdit()
        self.padding_value_input.setText("0x00")
        self.padding_value_input.setMinimumHeight(30)
        self.padding_value_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        self.padding_value_input.editingFinished.connect(self.on_padding_value_input_finished)
        self.default_toolbar_layout.addWidget(self.padding_value_input, 1)

        self.convert_dropdown = QComboBox()
        self.convert_dropdown.setMinimumHeight(30)
        self.convert_dropdown.addItem("Default")  # Add default text as first item
        self.convert_dropdown.addItems(CommonData.SUPPORTED_FIRMWARE_FORMAT)
        self.convert_dropdown.setCurrentIndex(0)  # Set default to "Format"
        self.convert_dropdown.setFixedWidth(80)
        self.convert_dropdown.setFixedHeight(30)
        self.convert_dropdown.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ccc;
                background-color: white;
                selection-background-color: #e0e0e0;
            }
        """)
        self.default_toolbar_layout.addWidget(self.convert_dropdown, 1)

        self.default_cut_btn = QPushButton("Cut")
        self.default_cut_btn.setMinimumHeight(30)
        self.default_cut_btn.setStyleSheet(self.get_button_style())
        self.default_toolbar_layout.addWidget(self.default_cut_btn, 1)
        self.default_cut_btn.clicked.connect(self.default_cut_action)

        self.padding_btn = QPushButton("Pad")
        self.padding_btn.setMinimumHeight(30)
        self.padding_btn.setStyleSheet(self.get_button_style())
        self.default_toolbar_layout.addWidget(self.padding_btn, 1)
        self.padding_btn.clicked.connect(self.padding_action)

        self.convert_file_btn = QPushButton("Convert")
        self.convert_file_btn.setMinimumHeight(30)
        self.convert_file_btn.setStyleSheet(self.get_button_style())
        self.default_toolbar_layout.addWidget(self.convert_file_btn, 1)
        self.convert_file_btn.clicked.connect(self.convert_action)

        self.default_panel_layout.addWidget(self.default_toolbar)

    def init_default_table(self):
        self.default_table = QTableView(self.default_panel)
        self.default_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        self.default_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Model: 0 rows, 5 columns
        self.default_table_model = QStandardItemModel(0, 5)
        self.default_table_model.setHorizontalHeaderLabels(
            ["File Name", "Start Address", "DLC", "CRC", "Note"]
        )

        # Set table model
        self.default_table.setModel(self.default_table_model)
        self.default_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Set style
        self.default_table.setStyleSheet("""
            QTableView {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #c0c0c0;
                font-weight: bold;
            }
            QTableView::item:selected:active {
                background: blue;
                color: white;
            }
        """)

        # Make table headers resizable
        header = self.default_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)

        # Set minimum column widths
        self.default_table.setColumnWidth(0, 300)
        self.default_table.setColumnWidth(1, 120)
        self.default_table.setColumnWidth(2, 100)
        self.default_table.setColumnWidth(3, 100)
        self.default_table.setColumnWidth(4, 100)

        self.default_panel_layout.addWidget(self.default_table)

    def init_cutting_panel_ui(self):
        self.cutting_panel = QWidget(parent=self)
        self.cutting_panel_layout = QVBoxLayout(self.cutting_panel)

        cutting_panel_label = QLabel("Custom Cut Configuration")
        cutting_panel_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        self.cutting_panel_layout.addWidget(cutting_panel_label)

        self.init_cutting_toolbar()
        self.init_cutting_table()

        self.splitter.addWidget(self.cutting_panel)

    def init_cutting_toolbar(self):
        self.cutting_toolbar = QWidget(self.cutting_panel)
        self.cutting_toolbar_layout = QHBoxLayout(self.cutting_toolbar)

        self.bin_start_addr_input = QLineEdit()
        self.bin_start_addr_input.setText("0x00000000")
        self.bin_start_addr_input.setMinimumHeight(30)
        self.bin_start_addr_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        self.bin_start_addr_input.editingFinished.connect(self.on_bin_str_addr_input_finished)
        self.cutting_toolbar_layout.addWidget(self.bin_start_addr_input, 1)

        self.plus_btn = QPushButton("+")
        self.plus_btn.setMinimumHeight(30)
        self.plus_btn.setStyleSheet(self.get_button_style())
        self.cutting_toolbar_layout.addWidget(self.plus_btn, 1)
        self.plus_btn.clicked.connect(self.plus_action)

        self.load_config_btn = QPushButton("Load Config")
        self.load_config_btn.setMinimumHeight(30)
        self.load_config_btn.setStyleSheet(self.get_button_style())
        self.cutting_toolbar_layout.addWidget(self.load_config_btn, 1)
        self.load_config_btn.clicked.connect(self.load_cut_configuration)

        self.save_config_btn = QPushButton("Save Config")
        self.save_config_btn.setMinimumHeight(30)
        self.save_config_btn.setStyleSheet(self.get_button_style())
        self.cutting_toolbar_layout.addWidget(self.save_config_btn, 1)
        self.save_config_btn.clicked.connect(self.save_config_action)

        self.custom_cut_btn = QPushButton("Custom cut")
        self.custom_cut_btn.setMinimumHeight(30)
        self.custom_cut_btn.setStyleSheet(self.get_button_style())
        self.cutting_toolbar_layout.addWidget(self.custom_cut_btn, 1)
        self.custom_cut_btn.clicked.connect(self.custom_cut_action)

        self.cutting_panel_layout.addWidget(self.cutting_toolbar)

    def init_cutting_table(self):
        self.cutting_table = QTableView(self.cutting_panel)
        self.cutting_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        self.cutting_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Model: 0 rows, 6 columns
        self.cutting_table_model = QStandardItemModel(0, 8)
        self.cutting_table_model.setHorizontalHeaderLabels(
            ["Start Address", "DLC Value", "Padding", "Padding Value", "Output Format", "Cut", "Delete Config", "Output Path"]
        )

        # Set table model
        self.cutting_table.setModel(self.cutting_table_model)

        # Set style
        self.cutting_table.setStyleSheet("""
            QTableView {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #c0c0c0;
                font-weight: bold;
            }
            QTableView::item:selected:active {
                background: blue;
                color: white;
            }
        """)

        # Make table headers resizable
        header = self.cutting_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)

        # Set minimum column widths
        self.cutting_table.setColumnWidth(0, 120)
        self.cutting_table.setColumnWidth(1, 80)
        self.cutting_table.setColumnWidth(2, 80)
        self.cutting_table.setColumnWidth(3, 100)
        self.cutting_table.setColumnWidth(4, 100)
        self.cutting_table.setColumnWidth(5, 100)
        self.cutting_table.setColumnWidth(6, 100)
        self.cutting_table.setColumnWidth(7, 100)
        self.cutting_panel_layout.addWidget(self.cutting_table)

    def get_button_style(self):
        """Return standard button style for gray theme"""
        return """
            QPushButton {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d8d8d8;
            }
            QPushButton:pressed {
                background-color: #c8c8c8;
            }
        """

    # Button action methods
    def select_software_file(self):
        settings = QSettings("MC-Flashing")

        last_dir = settings.value("last_open_dir", "")
        all_exts = CommonData.SUPPORTED_FIRMWARE_FORMAT
        filter_str = "Firmware Files (" + " ".join(f"*{ext}" for ext in all_exts) + ")"

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Software File",
            last_dir,
            filter_str
        )

        if file_path:
                new_dir = os.path.dirname(file_path)

                # Nếu thư mục mới → clear bảng
                self.default_table_model.removeRows(0, self.default_table_model.rowCount())

                # Lưu lại thư mục vừa chọn
                settings.setValue("last_open_dir", new_dir)

                self.file_path_input.setText(file_path)
                self.add_file_to_table(file_path)
                # self.console_log.append(f"Selected file: {os.path.basename(file_path)}")

    def add_file_to_table(self, file_path):
        is_bin = any(str(file_path).lower().endswith(fmt) for fmt in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT)
        base = CommonData.DEFAULT_BIN_START_ADDRESS if is_bin else None 
        # Call API : services.firmware 
        plan = auto_load_segment(
            str(file_path),
            output_format=None,
            bin_base_address=base,
            pad_byte_for_crc=0xFF,
            print_rows=True,      # không in ra console trong unit test
            write_index_csv=False,  # không ghi CSV trong unit test
            show_input_info=True
        )
        for item in plan:
            row_count = self.default_table_model.rowCount()
            row_items = [
                QStandardItem(str(item.filename)),
                QStandardItem(str(f"0x{item.start_address:08X}")),
                QStandardItem(str(f"0x{item.dlc:08X}")),
                QStandardItem(str(f"0x{item.crc32:08X}")),
                QStandardItem(str(item.note))
            ]
            self.default_table_model.appendRow(row_items)
            
        if row_count == 1:
            self.default_table_model.removeRow(1)

    def default_cut_action(self):
        file_path = self.file_path_input.text()
        if not any(format in file_path for format in CommonData.SUPPORTED_FIRMWARE_FORMAT):
            QMessageBox.warning(self, "Warning", "Invalid input file")
            return

        if self.convert_dropdown.currentText() == "Default":
            output_format = None
        else:
            output_format = self.convert_dropdown.currentText()

        try:
            plan = auto_load_segment(
                str(file_path),
                output_format=output_format,
                print_rows=False,
                write_index_csv=False,
                show_input_info=False
            )

            written = do_cut_sw_file(
                str(file_path),
                plan,
                output_dir=None,
                bin_base_address=None
            )

            if written:
                self.default_table_model.removeRows(1, self.default_table_model.rowCount() - 1)
                for item in plan:
                    row_items = [
                        QStandardItem(str(item.filename)),
                        QStandardItem(str(f"0x{item.start_address:08X}")),
                        QStandardItem(str(f"0x{item.dlc:08X}")),
                        QStandardItem(str(f"0x{item.crc32:08X}")),
                        QStandardItem(str(item.note))
                    ]
                    self.default_table_model.appendRow(row_items)
                QMessageBox.information(self, "Successful", f"Successfully cutting")
            else:
                QMessageBox.information(self, "Failed", f"Fail to cut")

        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Cutting failed: {e}")
            return

    def padding_action(self):
        file_path = self.file_path_input.text()
        if not any(format in file_path for format in CommonData.SUPPORTED_FIRMWARE_FORMAT):
            QMessageBox.warning(self, "Warning", "Invalid input file")
            return

        try:
            text_value = self.padding_value_input.text()
            if (int(text_value, 16) < 0) or (int(text_value, 16) > 255):
                QMessageBox.warning(self, "Warning", "Padding value must be in range 0x00-0xFF")
                return
            else:
                padding_value = int(text_value, 16)
        except ValueError:
            QMessageBox.warning(self, "Warning", "Padding value must be in range 0x00-0xFF")
            return

        if self.convert_dropdown.currentText() == "Default":
            output_format = None
        else:
            output_format = self.convert_dropdown.currentText()

        try:
            padded_file_path = padding_firmware(file_path, padding_value, None, output_format)
            QMessageBox.information(self, "Successfully padded", f"Padded file: {padded_file_path}")
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Padding failed: {e}")
            return

    def get_save_format_preference(self):
        """Ask user to choose save format"""
        formats = ["JSON (.json)", "Excel (.xlsx)"]
        format_choice, ok = QInputDialog.getItem(
            self, "Choose Format", "Select configuration file format:",
            formats, 0, False
        )

        if ok:
            return "json" if "JSON" in format_choice else "excel"
        return None

    def load_cut_configuration(self):
        """Load cutting table configuration from JSON or Excel file"""
        source_file = self.file_path_input.text()
        if source_file and os.path.exists(source_file):
            default_dir = os.path.dirname(source_file)
        else:
            # Fallback to user's documents folder
            default_dir = os.path.expanduser("~/Documents")

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load Configuration",
            default_dir,
            self.config_manager.get_file_dialog_filter()
        )

        if not file_path:
            return  # User cancelled

        try:
            # Detect format and load data
            format_type = self.config_manager.detect_file_format(file_path)
            json_data = self.config_manager.load_configuration(file_path)

            configurations = json_data["configurations"]
            global_configration = json_data["global_configration"]
            if len(configurations) == 0:
                QMessageBox.warning(self, "Warning", "The configuration file contains no configurations to load.")
                return

        except FileNotFoundError:
            QMessageBox.critical(self, "File Error", f"The selected file could not be found:\n\n{file_path}")
            return
        except PermissionError:
            QMessageBox.critical(self, "Permission Error", f"Cannot read the selected file. Please check file permissions:\n\n{file_path}")
            return
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"An error occurred while loading the configuration:\n\n{str(e)}")
            return

        try:
            bin_base_address = global_configration.get("bin_base_address", "0x00000000")
            self.bin_start_addr_input.setText(bin_base_address)
            # Clear the existing table completely
            self.cutting_table_model.clear()

            # Reinitialize table headers
            headers = ["Start Address", "DLC Value", "Padding", "Padding Value", "Output Format", "Cut", "Delete Config", "Output Path"]
            self.cutting_table_model.setHorizontalHeaderLabels(headers)

            # Load each configuration
            loaded_count = 0
            for config in configurations:
                # Add a new row
                self.add_command_row()
                current_row = self.cutting_table_model.rowCount() - 1

                # Populate the row with loaded data
                self.populate_row_from_config(current_row, config)
                loaded_count += 1

            success_message = f"Configuration loaded successfully!\n\n"
            success_message += f"File: {os.path.basename(file_path)}\n"
            success_message += f"Format: {format_type.upper()}\n"
            success_message += f"Configurations loaded: {loaded_count}\n"

            QMessageBox.information(self, "Load Successful", success_message)

        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"An error occurred while populating the table:\n\n{str(e)}")
            return

    def populate_row_from_config(self, row_index, config):
        """Populate a table row with data from a configuration object"""
        try:
            # Set Start Address (column 0)
            start_address = config.get("start_address", "")
            if self.cutting_table_model.item(row_index, 0):
                self.cutting_table_model.item(row_index, 0).setText(start_address)

            # Set DLC Value (column 1)
            dlc_value = config.get("DLC", "")
            if self.cutting_table_model.item(row_index, 1):
                self.cutting_table_model.item(row_index, 1).setText(dlc_value)

            # Set Padding checkbox (column 2)
            is_padding = config.get("isPadding", False)
            padding_checkbox = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 2))
            if padding_checkbox:
                padding_checkbox.setChecked(is_padding)

            # Set Padding Value (column 3)
            padding_value = config.get("padding_value", "0x00")
            padding_input = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 3))
            if padding_input:
                padding_input.setText(padding_value)
                # Enable/disable based on checkbox state
                padding_input.setEnabled(is_padding)

            # Set Output Format (column 4)
            format_value = config.get("format", None)
            format_dropdown = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 4))
            if format_dropdown:
                if format_value:
                    # Find the format in the dropdown items
                    format_index = format_dropdown.findText(format_value)
                    if format_index >= 0:
                        format_dropdown.setCurrentIndex(format_index)
                    else:
                        # If format not found, default to hex
                        hex_index = format_dropdown.findText("hex")
                        if hex_index >= 0:
                            format_dropdown.setCurrentIndex(hex_index)
                else:
                    # Default to hex for non-format configurations
                    hex_index = format_dropdown.findText("hex")
                    if hex_index >= 0:
                        format_dropdown.setCurrentIndex(hex_index)

            # Columns 5-6 (Cut/Delete buttons) are handled by create_row_widgets
            # Column 7 (Output Path) is left empty as requested

        except Exception as e:
            raise Exception(f"Failed to populate row {row_index + 1}: {str(e)}")

    def plus_action(self):
        # Check if any files are selected
        if self.default_table.model().rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No files chosen yet")
            return

        # self.console_log.append("Adding new command row")
        self.add_command_row()

    def validation_cut_configuration(self, source_file, total_rows) -> list[str]:
        validation_errors = []
        if total_rows == 0:
            validation_errors.append("No configurations to process. Please add rows using the '+' button.")
            return validation_errors
        if not source_file:
            validation_errors.append("Please select a source file first in the Default Configuration section.")
            return validation_errors
        for row_index in range(total_rows):
            # Get Start Address and DLC Value
            start_addr_item = self.cutting_table_model.item(row_index, 0)
            dlc_val_item = self.cutting_table_model.item(row_index, 1)

            start_addr = start_addr_item.text().strip() if start_addr_item else ""
            dlc_val = dlc_val_item.text().strip() if dlc_val_item else ""

            # Check for missing required fields
            if not start_addr:
                validation_errors.append(f"Row {row_index + 1}: Start Address is required")
            if not dlc_val:
                validation_errors.append(f"Row {row_index + 1}: DLC Value is required")

            # Validate format of Start Address and DLC Value if provided
            if start_addr:
                try:
                    int(start_addr, 16)  # Try to parse as hex
                except ValueError:
                    validation_errors.append(f"Row {row_index + 1}: Start Address must be a valid hexadecimal value")

            if dlc_val:
                try:
                    int(dlc_val, 16)  # Try to parse as hex
                except ValueError:
                    validation_errors.append(f"Row {row_index + 1}: DLC Value must be a valid hexadecimal value")
            padding_checkbox = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 2))
            padding_enabled = padding_checkbox.isChecked() if padding_checkbox else False

            # Get padding value from input widget
            if padding_enabled:
                padding_input = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 3))
                try:
                    padding_value = int(padding_input.text(), 16)
                    if padding_value < 0x00 or padding_value > 0xFF:
                        validation_errors.append(f"Row {row_index + 1}: Padding value must be in range 0x00 ~ 0xFF")
                except ValueError:
                        validation_errors.append(f"Row {row_index + 1}: Padding value must be in range 0x00 ~ 0xFF")
        return validation_errors
    
    def custom_cut_action(self):
        """Process all rows in the cutting table sequentially"""
        total_rows = self.cutting_table_model.rowCount()
        source_file = self.file_path_input.text()
        validation_errors = self.validation_cut_configuration(source_file, total_rows)
        
        # If there are validation errors, show them and stop
        if validation_errors:
            error_message = "Please fix the following issues before processing:\n\n" + "\n".join(validation_errors)
            QMessageBox.warning(self, "Validation Error", error_message)
            return

        successful_cuts = []
        failed_cuts = []

        for row_index in range(total_rows):
            try:
                # Extract configuration data (same logic as cut_action_from_table_new)
                start_addr = self.cutting_table_model.item(row_index, 0).text()
                dlc_val = self.cutting_table_model.item(row_index, 1).text()

                # Get checkbox state
                padding_checkbox = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 2))
                padding_enabled = padding_checkbox.isChecked() if padding_checkbox else False

                # Get padding value from input widget
                if padding_enabled:
                    padding_input = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 3))
                    try:
                        padding_value = int(padding_input.text(), 16)
                        if padding_value < 0x00 or padding_value > 0xFF:
                            QMessageBox.warning(self, "Invalid padding value",f"Row {row_index + 1}: Padding value must be in range 0x00 ~ 0xFF")
                            return
                    except ValueError:
                            QMessageBox.warning(self, "Invalid padding value",f"Row {row_index + 1}: Padding value must be in range 0x00 ~ 0xFF")
                            return
                else:
                    padding_value = None

                # Get output format from dropdown
                format_dropdown = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 4))

                if format_dropdown.currentText() == "Default":
                    output_format = None
                else:
                    output_format = format_dropdown.currentText()

                # Get bin start address
                bin_start_addr = int(self.bin_start_addr_input.text(), 16)

                # Call the cutting service
                cut_file = do_cut_sw_file_with_format(
                    source_file,
                    int(start_addr, 16),
                    int(dlc_val, 16),
                    format=output_format,
                    isPadding=padding_enabled,
                    padding_value=padding_value,
                    bin_base_address=bin_start_addr
                )

                # Set output path
                output_path = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 7))
                output_path.setText(str(cut_file))

                successful_cuts.append({
                    'row': row_index + 1,
                    'start_addr': start_addr,
                    'dlc_val': dlc_val,
                    'format': output_format,
                    'output_file': str(cut_file)
                })

            except Exception as e:
                failed_cuts.append({
                    'row': row_index + 1,
                    'error': str(e)
                })

        self.show_custom_cut_results(successful_cuts, failed_cuts)

    def save_config_action(self):
        """Save the current cutting table configuration to JSON or Excel file"""
        total_rows = self.cutting_table_model.rowCount()
        source_file = self.file_path_input.text()
        validation_errors = self.validation_cut_configuration(source_file, total_rows)
        
        # If there are validation errors, show them and stop
        if validation_errors:
            error_message = "Please fix the following issues before processing:\n\n" + "\n".join(validation_errors)
            QMessageBox.warning(self, "Validation Error", error_message)
            return

        format_choice = self.get_save_format_preference()
        if format_choice is None:
            return  # User cancelled format selection

        # Get current timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if format_choice == "json":
            default_filename = f"cut_configuration_{timestamp}.json"
        else:
            default_filename = f"cut_configuration_{timestamp}.xlsx"

        default_path = os.path.join(source_file, default_filename)

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Configuration",
            default_path,
            self.config_manager.get_save_dialog_filter(format_choice)
        )

        if not file_path:
            return  # User cancelled

        try:
            configurations = []

            for row_index in range(total_rows):
                # Extract data using same pattern as custom_cut_action
                start_addr = self.cutting_table_model.item(row_index, 0).text()
                dlc_val = self.cutting_table_model.item(row_index, 1).text()

                # Get checkbox state
                padding_checkbox = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 2))
                is_padding = padding_checkbox.isChecked() if padding_checkbox else False

                # Get padding value from input widget
                padding_input = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 3))
                padding_value = padding_input.text() if padding_input else "0x00"

                # Get output format from dropdown
                format_dropdown = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 4))
                format_text = format_dropdown.currentText() if format_dropdown else "hex"

                # Create configuration object
                config = {
                    "start_address": start_addr,
                    "DLC": dlc_val,
                    "isPadding": is_padding,
                    "padding_value": padding_value,
                    "format": format_text
                }
                configurations.append(config)
            bin_base_address = self.bin_start_addr_input.text() or "0x00000000"
            config_data = {
                "global_configuration": {
                    "bin_base_address": bin_base_address
                },
                "configurations": configurations
            }

            self.config_manager.save_configuration(config_data, file_path, format_choice)

            QMessageBox.information(
                self,
                "Save Successful",
                f"Configuration saved successfully!\n\nFile: {os.path.basename(file_path)}\nFormat: {format_choice.upper()}\nConfigurations: {len(configurations)}"
            )

        except PermissionError:
            QMessageBox.critical(
                self,
                "Permission Error",
                f"Cannot write to the selected file. Please check file permissions or choose a different location.\n\nFile: {file_path}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Save Error",
                f"An error occurred while saving the configuration:\n\n{str(e)}"
            )

    def convert_action(self):
        # 1. Lấy file input
        input_path = self.file_path_input.text().strip()
        if not input_path or not os.path.isfile(input_path):
            QMessageBox.warning(self, "Warning", "Input is invalid!")
            return

        # 2. Lấy định dạng output
        int_fmt = Path(input_path).suffix.lower()
        out_fmt = self.convert_dropdown.currentText()
        addrs_value = CommonData.DEFAULT_BIN_START_ADDRESS

        if out_fmt == "Default" or int_fmt == out_fmt or not out_fmt:
            QMessageBox.warning(self, "Warning", "Output is NOT chosen!")
            return

        if int_fmt in CommonData.SUPPORTED_FIRMWARE_BIN_FORMAT and (out_fmt in CommonData.SUPPORTED_FIRMWARE_REC_FORMAT or out_fmt in CommonData.SUPPORTED_FIRMWARE_HEX_FORMAT):
            valueOk = False
            while (valueOk == False):
                addrs_text, ok = QInputDialog.getText(self.default_panel, "Bin Start Address",
                                            "Range 0x0000000 ~ 0xFFFFFFFF:", text="0x0000000")
                if ok and addrs_text:
                    try:
                        addrs_value = int(addrs_text, 16)
                        if 0x00000000 <= addrs_value <= 0xFFFFFFFF:
                            print(addrs_value)
                            valueOk = True
                    except ValueError:
                        continue
                else:
                    return

        # 3. Gọi API convert
        try:
            results = convert_firmware(
                file_path=input_path,
                output_format=out_fmt,    # ví dụ "hex", "bin", "s19", ...
                output_path=None,         # None = auto lưu cạnh file input
                bin_start_addrs=addrs_value
            )
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Convert fail:\n{e}")
            return

        # 4. Thông tin kết quả
        msg = f"Output:\n"
        for result in results:
            msg = msg + result + "\n"
        QMessageBox.information(self, "Convert Successful", msg)

    def add_command_row(self):
        """Add a new command row to the cutting table model with proper widgets"""
        # Create a new row with default values
        row_items = [
            QStandardItem(""),  # Start Address - editable text
            QStandardItem(""),  # DLC Value - editable text
            QStandardItem(""),  # Padding - will be replaced with checkbox
            QStandardItem(""),  # Padding Value - editable text (initially disabled)
            QStandardItem(""),  # Output Format - will be replaced with dropdown
            QStandardItem(""),  # Cut - will be replaced with button
            QStandardItem(""),  # Delete - will be replaced with button
            QStandardItem("")   # Output Path - editable text
        ]

        # Make text items editable
        for i, item in enumerate(row_items):
            if i in [0, 1, 3, 7]:  # Start Address, DLC Value, Padding Value, Output Path
                item.setEditable(True)
            else:
                item.setEditable(False)

        # Add the row to the model
        self.cutting_table_model.appendRow(row_items)

        # Get the new row index
        new_row_index = self.cutting_table_model.rowCount() - 1

        # Create and set custom widgets for specific columns
        self.create_row_widgets(new_row_index)

        # Scroll to the newly added row
        model_index = self.cutting_table_model.index(new_row_index, 0)
        self.cutting_table.scrollTo(model_index)

    def create_row_widgets(self, row_index):
        """Create and set custom widgets for a specific row"""

        # Column 2: Padding Checkbox
        padding_checkbox = QCheckBox()
        padding_checkbox.setStyleSheet("""
            QCheckBox {
                margin: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid gray;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid gray;
                background: blue;
            }
        """)

        # Column 3: Padding Value (initially disabled)
        padding_value_input = QLineEdit()
        padding_value_input.setText("0x00")
        padding_value_input.setEnabled(False)
        padding_value_input.setMaxLength(4)  # Limit to 4 characters
        padding_value_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f0f0f0;
                color: #888;
            }
            QLineEdit:enabled {
                background-color: white;
                color: black;
            }
        """)
        regex = QRegularExpression(r"^0x[0-9A-Fa-f]{0,2}$")
        padding_value_input.setValidator(QRegularExpressionValidator(regex, padding_value_input))

        # Connect checkbox to enable/disable padding value input
        padding_checkbox.stateChanged.connect(
            lambda state, input_field=padding_value_input: self.toggle_padding_input_new(state, input_field)
        )

        # Column 4: Output Format Dropdown
        format_dropdown = QComboBox()
        format_dropdown.addItem("Default")  # Add default text as first item
        format_dropdown.addItems(CommonData.SUPPORTED_FIRMWARE_FORMAT)
        format_dropdown.setCurrentIndex(0)
        format_dropdown.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
        """)

        # Column 5: Cut Button
        cut_button = QPushButton("Cut")
        cut_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 15px;
                font-weight: bold;
                border: none;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        cut_button.clicked.connect(lambda: self.cut_action_from_table_new(row_index))

        # Column 6: Delete Button
        delete_button = QPushButton("Delete")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 15px;
                font-weight: bold;
                border: none;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        delete_button.clicked.connect(lambda: self.delete_command_row_from_table_new(row_index))

        # Column 7: Output path
        output_path = QLineEdit()
        output_path.setReadOnly(True)
        output_path.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f0f0f0;
                color: #888;
            }
            QLineEdit:enabled {
                background-color: white;
                color: black;
            }
        """)

        # Set the widgets in the table
        self.cutting_table.setIndexWidget(self.cutting_table_model.index(row_index, 2), padding_checkbox)
        self.cutting_table.setIndexWidget(self.cutting_table_model.index(row_index, 3), padding_value_input)
        self.cutting_table.setIndexWidget(self.cutting_table_model.index(row_index, 4), format_dropdown)
        self.cutting_table.setIndexWidget(self.cutting_table_model.index(row_index, 5), cut_button)
        self.cutting_table.setIndexWidget(self.cutting_table_model.index(row_index, 6), delete_button)
        self.cutting_table.setIndexWidget(self.cutting_table_model.index(row_index, 7), output_path)

    def toggle_padding_input_new(self, state, padding_input):
        """Enable or disable padding input based on checkbox state"""
        if state == 2:  # Qt.CheckState.Checked
            padding_input.setEnabled(True)
        else:  # Qt.CheckState.Unchecked
            padding_input.setEnabled(False)
            padding_input.clear()  # Clear the input when disabled

    def cut_action_from_table_new(self, row_index):
        """Handle cut action for a specific table row using widgets"""
        # Get data from the table model and widgets
        start_addr_item = self.cutting_table_model.item(row_index, 0)
        dlc_val_item = self.cutting_table_model.item(row_index, 1)

        # Extract text and strip whitespace, handle None items
        start_addr = start_addr_item.text().strip() if start_addr_item else ""
        dlc_val = dlc_val_item.text().strip() if dlc_val_item else ""

        # Check if either field is empty or contains only whitespace
        if not start_addr or not dlc_val:
            missing_fields = []
            if not start_addr:
                missing_fields.append("Start Address")
            if not dlc_val:
                missing_fields.append("DLC Value")

            error_message = f"The following required fields are missing or empty:\n• {chr(10).join(missing_fields)}"
            QMessageBox.warning(self, "Missing Required Fields", error_message)
            return
        # Get checkbox state
        padding_checkbox = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 2))
        padding_enabled = padding_checkbox.isChecked() if padding_checkbox else False

        # Get padding value from input widget
        if padding_enabled:
            padding_input = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 3))
            try:
                padding_value = int(padding_input.text(), 16)
                if padding_value < 0x00 or padding_value > 0xFF:
                    QMessageBox.warning(self, "Invalid padding value",f"Row {row_index + 1}: Padding value must be in range 0x00 ~ 0xFF")
                    return
            except ValueError:
                    QMessageBox.warning(self, "Invalid padding value",f"Row {row_index + 1}: Padding value must be in range 0x00 ~ 0xFF")
                    return
        else:
            padding_value = None

        # Get output format from dropdown
        format_dropdown = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 4))

        if format_dropdown.currentText() == "Default":
            output_format = None
        else:
            output_format = format_dropdown.currentText()

        try:
            # Get bin start address
            bin_start_addr = int(self.bin_start_addr_input.text(), 16)

            # Call the cutting service
            cut_file = do_cut_sw_file_with_format(
                self.file_path_input.text(),
                int(start_addr, 16),
                int(dlc_val, 16),
                format=output_format,
                isPadding=padding_enabled,
                padding_value=padding_value,
                bin_base_address=bin_start_addr
            )
        except Exception as e:
            QMessageBox.warning(self, "Invalid input",f"{e}")
            return

        # Set output path
        output_path = self.cutting_table.indexWidget(self.cutting_table_model.index(row_index, 7))
        output_path.setText(str(cut_file))

    def delete_command_row_from_table_new(self, row_index):
        """Delete a command row from the table model"""
        reply = QMessageBox.question(self, "Confirm Delete",
                                   "Are you sure you want to delete this row?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # Remove the row from the model (this will also remove the widgets)
            self.cutting_table_model.removeRow(row_index)

            # Update button connections for remaining rows since row indices have changed
            self.update_button_connections_after_delete(row_index)

    def update_button_connections_after_delete(self, deleted_row_index):
        """Update button connections after a row is deleted"""
        # After deleting a row, all rows below it have their indices shifted up by 1
        # We need to reconnect the buttons with the correct row indices
        total_rows = self.cutting_table_model.rowCount()

        for row in range(deleted_row_index, total_rows):
            # Reconnect Cut button
            cut_button = self.cutting_table.indexWidget(self.cutting_table_model.index(row, 5))
            if cut_button:
                # Disconnect old connection and connect with new row index
                cut_button.clicked.disconnect()
                cut_button.clicked.connect(lambda _, r=row: self.cut_action_from_table_new(r))

            # Reconnect Delete button
            delete_button = self.cutting_table.indexWidget(self.cutting_table_model.index(row, 6))
            if delete_button:
                # Disconnect old connection and connect with new row index
                delete_button.clicked.disconnect()
                delete_button.clicked.connect(lambda _, r=row: self.delete_command_row_from_table_new(r))

    def on_padding_value_input_finished(self):
        text = self.padding_value_input.text()
        try:
            value = int(text, 16)
            if value < 0x00 or value > 0xFF:
                self.padding_value_input.setText("0x00")
        except ValueError:
            self.padding_value_input.setText("0x00")

    def on_bin_str_addr_input_finished(self):
        text = self.bin_start_addr_input.text()
        try:
            value = int(text, 16)
            if value < 0x00000000 or value > 0xFFFFFFFF:
                self.bin_start_addr_input.setText("0x00000000")
        except ValueError:
            self.bin_start_addr_input.setText("0x00000000")

    def show_custom_cut_results(self, successful_cuts, failed_cuts):
        """Display the results of the custom cut operation"""
        total_processed = len(successful_cuts) + len(failed_cuts)

        # Build result message
        result_message = f"Custom Cut Operation Complete\n"
        result_message += f"Total rows processed: {total_processed}\n"
        result_message += f"Successful: {len(successful_cuts)}\n"
        result_message += f"Failed: {len(failed_cuts)}\n\n"

        # Add successful cuts details
        if successful_cuts:
            result_message += "✅ Successful Cuts:\n"
            for cut in successful_cuts:
                result_message += f"  Row {cut['row']}: {cut['start_addr']} → {cut['output_file']}\n"
            result_message += "\n"

        # Add failed cuts details
        if failed_cuts:
            result_message += "❌ Failed Cuts:\n"
            for cut in failed_cuts:
                result_message += f"  Row {cut['row']}: {cut['error']}\n"

        # Show appropriate message box based on results
        if failed_cuts:
            QMessageBox.warning(self, "Custom Cut Results", result_message)
        else:
            QMessageBox.information(self, "Custom Cut Results", result_message)
