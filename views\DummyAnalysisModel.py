import sys
from PyQt6.QtCore import QObject, pyqtSignal, pyqtSlot, QMutex, QMutexLocker, QTimer, QEventLoop
from dataclasses import dataclass

@dataclass(frozen=True)
class IDPair:
    request_id: int
    response_id: int

class AnalyzerConfig(QObject):
    def __init__(self, id_pairs, p2_ms, p2_star_ms, channel, services):
        super().__init__()
        self.id_pairs = id_pairs
        self.p2_ms = p2_ms
        self.p2_star_ms = p2_star_ms
        self.channel = channel
        self.services = services

class CanFlashLogAnalyzer(QObject):
    """
    This is the 'real model' that performs analysis.
    It runs in a separate thread to avoid blocking the UI.
    """
    
    # SIGNALS emitted by the model
    progress_updated = pyqtSignal(int)      # Emits progress percentage (0-100)
    analysis_completed = pyqtSignal()    # Emits result when done
    analysis_cancelled = pyqtSignal()       # Emits when cancelled
    
    def __init__(self):
        super().__init__()
        self._is_cancelled = False
        self._mutex = QMutex()  # Thread-safe access to _is_cancelled
    
    def _sleep_with_events(self, milliseconds):
        """
        Sleep while processing events - this allows signals to be received!
        """
        loop = QEventLoop()
        QTimer.singleShot(milliseconds, loop.quit)
        loop.exec()
    
    @pyqtSlot()  # SLOT: receives signal from UI to start analysis
    def start_analysis(self):
        """
        This method is called when the 'analysis_requested' signal is received.
        It performs the analysis and emits progress updates.
        """
        with QMutexLocker(self._mutex):
            self._is_cancelled = False
        
        # Simulate a long-running analysis process
        for i in range(101):
            # Check cancellation BEFORE doing work (thread-safe)
            with QMutexLocker(self._mutex):
                if self._is_cancelled:
                    self.analysis_cancelled.emit()
                    return
            
            # Emit progress update
            self.progress_updated.emit(i)
            
            # Sleep while allowing event processing
            # Break into smaller chunks for more responsive cancellation
            for j in range(5):
                with QMutexLocker(self._mutex):
                    if self._is_cancelled:
                        self.analysis_cancelled.emit()
                        return
                # This sleep processes events, allowing cancel_analysis() to be called!
                self._sleep_with_events(10)  # 10ms sleep with event processing
        
        # Analysis complete
        self.analysis_completed.emit()
    
    @pyqtSlot()  # SLOT: receives signal from UI to cancel
    def cancel_analysis(self):
        """
        This method is called when the 'cancel_requested' signal is received.
        """
        with QMutexLocker(self._mutex):
            self._is_cancelled = True
