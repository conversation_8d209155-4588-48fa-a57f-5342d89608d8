from PyQt6.QtWidgets import (Q<PERSON><PERSON><PERSON>indow, QTabWidget, QWidget,
                             QVBoxLayout)
from views.CuttingPaddingView import CuttingPaddingView
from views.DllCheckingView import DllCheckingView
from views.CrcCalculationView import CrcCalculationView
from views.UdsAnalysisView import UdsAnalysisView

class MiniTool(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("MiniTool")
        self.setGeometry(100, 100, 1000, 700)

        # Set gray background for the main window
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QWidget {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #c0c0c0;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #f0f0f0;
                border-bottom: 1px solid #f0f0f0;
            }
        """)

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.cutting_padding_tab = CuttingPaddingView()
        self.dll_checking_tab = DllCheckingView()
        self.crc_checking_tab = CrcCalculationView()
        self.uds_analysis_tab = UdsAnalysisView()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.cutting_padding_tab, "Cutting/Padding")
        self.tab_widget.addTab(self.dll_checking_tab, "DLL Checking")
        self.tab_widget.addTab(self.crc_checking_tab, "CRC Checking")
        self.tab_widget.addTab(self.uds_analysis_tab, "UDS Analysis")

        # Set main layout
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tab_widget)
        central_widget.setLayout(main_layout)
