from PyQt6.QtWidgets import (QComboBox, QListView)
from PyQt6.QtGui import (QStandardItemModel, QStandardItem)
from PyQt6.QtCore import (Qt, QEvent, pyqtSignal)
class MultiSelectCombo(QComboBox):
    """ComboBox chọn nhiều IDs (checkable)."""
    selectionChanged = pyqtSignal(list)  # emit danh sách ID đã chọn (str)

    def __init__(self, ids: list[str], include_all: bool = True, parent=None):
        super().__init__(parent)
        self.setView(QListView(self))

        self._m = QStandardItemModel(self)
        self.setModel(self._m)
        self.include_all = include_all

        self.setEditable(True)
        self.lineEdit().setReadOnly(True)
        self.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)

        if include_all:
            all_item = QStandardItem("All IDs")
            all_item.setFlags(Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsUserCheckable)
            all_item.setCheckState(Qt.CheckState.Checked)
            self._m.appendRow(all_item)

        for s in ids:
            it = QStandardItem(s)
            it.setFlags(Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsSelectable)
            it.setCheckState(Qt.CheckState.Checked if include_all else Qt.CheckState.Unchecked)
            self._m.appendRow(it)

        # Bắt sự kiện click trong popup để toggle check
        self.view().viewport().installEventFilter(self)
        self._update_text()

    # ---- Filter ----
    def eventFilter(self, obj, event):
        if obj is self.view().viewport() and event.type() == QEvent.Type.MouseButtonRelease:
            idx = self.view().indexAt(event.pos())
            if idx.isValid():
                self._toggle(idx)
                return True
        return super().eventFilter(obj, event)

    def _toggle(self, index):
        it = self._m.itemFromIndex(index)
        new_state = Qt.CheckState.Unchecked if it.checkState() == Qt.CheckState.Checked else Qt.CheckState.Checked
        it.setCheckState(new_state)

        if self.include_all and index.row() == 0:
            # Toggle tất cả theo "All IDs"
            for r in range(1, self._m.rowCount()):
                self._m.item(r).setCheckState(new_state)
        elif self.include_all:
            # Đồng bộ trạng thái "All IDs"
            all_on = all(self._m.item(r).checkState() == Qt.CheckState.Checked for r in range(1, self._m.rowCount()))
            self._m.item(0).setCheckState(Qt.CheckState.Checked if all_on else Qt.CheckState.Unchecked)

        self._update_text()
        self.selectionChanged.emit(self.selected_ids())

    # ---- API ----
    def selected_ids(self) -> list[str]:
        start = 1 if self.include_all else 0
        
        return [
            self._m.item(r).text()
            for r in range(start, self._m.rowCount())
            if self._m.item(r).checkState() == Qt.CheckState.Checked
        ]

    def set_selected_ids(self, ids: list[str]):
        start = 1 if self.include_all else 0
        all_on = True
        for r in range(start, self._m.rowCount()):
            it = self._m.item(r)
            st = Qt.CheckState.Checked if it.text() in ids else Qt.CheckState.Unchecked
            it.setCheckState(st)
            if st == Qt.CheckState.Unchecked:
                all_on = False
        if self.include_all:
            self._m.item(0).setCheckState(Qt.CheckState.Checked if all_on else Qt.CheckState.Unchecked)

        self._update_text()
        self.selectionChanged.emit(self.selected_ids())

    def uncheck_id(self, id_text: str):
        """Bỏ chọn 1 ID theo text (dùng cho nút Delete trong bảng)."""
        start = 1 if self.include_all else 0
        for r in range(start, self._m.rowCount()):
            it = self._m.item(r)
            if it.text() == id_text:
                it.setCheckState(Qt.CheckState.Unchecked)
                break
        if self.include_all:
            all_on = all(self._m.item(r).checkState() == Qt.CheckState.Checked for r in range(1, self._m.rowCount()))
            self._m.item(0).setCheckState(Qt.CheckState.Checked if all_on else Qt.CheckState.Unchecked)
        self._update_text()
        self.selectionChanged.emit(self.selected_ids())

    def _update_text(self):
        if self.include_all and self._m.item(0).checkState() == Qt.CheckState.Checked:
            text = "All IDs"
        else:
            ids = self.selected_ids()
            if not ids:
                text = "Select IDs"
            else:
                text = f"{len(ids)} selected" 
              
        self.lineEdit().setText(text)
        self.lineEdit().setCursorPosition(0)