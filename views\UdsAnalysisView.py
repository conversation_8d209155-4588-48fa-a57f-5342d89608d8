# pip install PyQt6
import json
from datetime import datetime
from PyQt6.QtCore import (Qt, QTimer, QObject, pyqtSignal, QThread, pyqtSlot, QMutex, QMutexLocker, QTimer, QEventLoop)
from PyQt6.QtWidgets import (QInputDialog, QApplication, QMainWindow, QCheckBox, QWidget, QVBoxLayout, QLabel, QSplitter, QSizePolicy, QLineEdit, QComboBox, QPushButton, QHBoxLayout, QFileDialog, QMessageBox, QProgressBar, QTableView, QHeaderView, QFrame, QGraphicsDropShadowEffect, QAbstractItemView, QProgressDialog)
from PyQt6.QtGui import (QColor, QStandardItemModel, QStandardItem)
from views.MultiSelectCombo import MultiSelectCombo
from common import CommonData
from services.configuration.ConfiguratioManager import GenericConfigurationManager
from services.configuration.UdsAnalysisConfigurationSchema import UdsAnalysisConfigurationSchema

from pathlib import Path
import os, pytest
# TODO: Model and service used for UDS analysis will be added later
# from services.can.can_flash_log_analyzer import CanFlashLogAnalyzer
# from models.config_can_log_analyzer import AnalyzerConfig, IDPair
# from models.results_can_log_analyzer import Progress
# import just for simulation purpose
from views.DummyAnalysisModel import CanFlashLogAnalyzer, IDPair, AnalyzerConfig

# ================== Dictionary data for analysis configuration ===================
analysis_table = {
    "request": ["Request ID", 0],
    "response": ["Response ID", 1],
    "service": ["Services", 2],
    "delete": ["Delete", 3]
}

# ================== MainWindow ==================
class UdsAnalysisView(QMainWindow):

    # SIGNALS emitted by the view
    analysis_requested = pyqtSignal(AnalyzerConfig)  # Emitted when button is clicked
    cancel_requested = pyqtSignal()    # Emitted when progress dialog is cancelled

    def __init__(self):
        super().__init__()
        self.setWindowTitle("UDS Analysis")
        self.resize(1200, 720)
        self.config_manager = GenericConfigurationManager(UdsAnalysisConfigurationSchema())

        # ----- TOP -----
        top_horizontal = QSplitter(Qt.Orientation.Horizontal)
        left_panel  = self.build_top_left_controls()
        right_panel = self.build_top_right_controls()  
        top_horizontal.addWidget(left_panel)
        top_horizontal.addWidget(right_panel)
        top_horizontal.setChildrenCollapsible(False)
        top_horizontal.setStretchFactor(0, 2)
        top_horizontal.setStretchFactor(1, 1)

        top_horizontal.setMaximumHeight(210)                    
        top_horizontal.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        # ----- BOTTOM -----
        bottom_panel = self.build_bottom_table_panel()

        # ----- SPLITTER -----
        main_vertical = QSplitter(Qt.Orientation.Vertical)
        main_vertical.addWidget(top_horizontal)  
        main_vertical.addWidget(bottom_panel)    
        main_vertical.setChildrenCollapsible(False)
        main_vertical.setStretchFactor(0, 1)
        main_vertical.setStretchFactor(1, 8)

        central = QWidget()
        cv = QVBoxLayout(central)
        cv.setContentsMargins(8, 8, 8, 8)
        cv.addWidget(main_vertical)
        self.setCentralWidget(central)

        QTimer.singleShot(0, lambda: (top_horizontal.setSizes([2, 1]),
                                      main_vertical.setSizes([1, 8])))

        self.progress_dialog = None
        self.setup_model()
        self.connect_signals()

    def setup_model(self):
        """ Create the analysis model and move it to a worker thread """
        self.worker_thread = QThread()
        self.analyzer = CanFlashLogAnalyzer()
        self.analyzer.moveToThread(self.worker_thread)
        self.worker_thread.start()

    def connect_signals(self):
        """ Connect all signals and slots """
        self.analysis_requested.connect(self.analyzer.start_analysis)
        self.cancel_requested.connect(self.analyzer.cancel_analysis)
        self.analyzer.progress_updated.connect(self.update_progress)
        self.analyzer.analysis_completed.connect(self.on_analysis_completed)
        self.analyzer.analysis_cancelled.connect(self.on_analysis_cancelled)

    def get_button_style(self):
        """Return standard button style for gray theme"""
        return """
            QPushButton {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d8d8d8;
            }
            QPushButton:pressed {
                background-color: #c8c8c8;
            }
        """
    
    # -------------------- UI: TOP-LEFT CONTROLS ----------------------
    def build_top_left_controls(self) -> QWidget:
        # -------- 0) Title: CAN Configuartion --------
        title_CAN = QLabel("<b>CAN Configuration</b>")
        title_CAN.setStyleSheet("font-size: 14pt; font-weight: bold;")

        # -------- 1) Panel: Select CAN Log --------
        box_log = QWidget()
        log_lay = QHBoxLayout(box_log)
        log_lay.setContentsMargins(8,6,8,6)
        log_lay.setSpacing(8)

        self.editLogPath = QLineEdit()
        self.editLogPath.setPlaceholderText("Path/to/Can_LogFile")
        self.editLogPath.setMinimumHeight(30)
        self.editLogPath.setReadOnly(True)
        self.editLogPath.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        btnBrowse = QPushButton("Browse")
        btnBrowse.setMinimumHeight(30)
        btnBrowse.setStyleSheet(self.get_button_style())
        btnBrowse.clicked.connect(self._browse_log)

        Can_log = QLabel("CAN Log:")
        log_lay.addWidget(Can_log)

        log_lay.addWidget(self.editLogPath, 1)
        log_lay.addWidget(btnBrowse)

        # ------- 2) Panel: CAN Channel --------
        box_chan = QWidget()
        chan_lay = QHBoxLayout(box_chan)
        chan_lay.setContentsMargins(8,6,8,6)
        chan_lay.setSpacing(8)

        self.editChannel = QLineEdit(str(CommonData.DEFAULT_CHANNEL))
        self.editChannel.setMinimumHeight(30)
        self.editChannel.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        Can_channel = QLabel("Channel:")
        chan_lay.addWidget(Can_channel)
        chan_lay.addWidget(self.editChannel, 1)

        # P2
        w_p2 = QWidget()
        p2 = QHBoxLayout(w_p2); p2.setContentsMargins(8,6,8,6); p2.setSpacing(6)
        p2.addWidget(QLabel("P2(ms)"))

        self.P2_value = QLineEdit()
        self.P2_value.setText(str(CommonData.DEFAULT_P2_MS))
        self.P2_value.setMinimumHeight(30)
        self.P2_value.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        p2.addWidget(self.P2_value, 1)

        # P2*
        w_p2s = QWidget()
        p2s = QHBoxLayout(w_p2s); p2s.setContentsMargins(8,6,8,6); p2s.setSpacing(6)
        p2s.addWidget(QLabel("P2*(ms)"))

        self.P2S_value = QLineEdit()
        self.P2S_value.setText(str(CommonData.DEFAULT_P2STAR_MS))
        self.P2S_value.setMinimumHeight(30)
        self.P2S_value.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        p2s.addWidget(self.P2S_value, 1)

        # STmin
        w_stmin = QWidget()
        stmin = QHBoxLayout(w_stmin); stmin.setContentsMargins(8,6,8,6); stmin.setSpacing(6)
        stmin.addWidget(QLabel("STmin(ms)"))

        self.STmin_value = QLineEdit()

        self.STmin_value.setText(str(CommonData.DEFAULT_STMIN_MS))

        self.STmin_value.setMinimumHeight(30)
        self.STmin_value.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        stmin.addWidget(self.STmin_value, 1)

        #STmax
        w_stmax = QWidget()
        stmax = QHBoxLayout(w_stmax); stmax.setContentsMargins(8,6,8,6); stmax.setSpacing(6)
        stmax.addWidget(QLabel("STmax(ms)"))

        self.STmax_value = QLineEdit()

        self.STmax_value.setText(str(CommonData.DEFAULT_STMAX_MS))

        self.STmax_value.setMinimumHeight(30)
        self.STmax_value.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        stmax.addWidget(self.STmax_value, 1)

        # force
        w_force = QWidget()
        force = QHBoxLayout(w_force); force.setContentsMargins(8,6,8,6); force.setSpacing(6)
        force.addWidget(QLabel("Force"))

        force_checkbox = QCheckBox()
        force_checkbox.setStyleSheet("""
            QCheckBox {
                margin: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid gray;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid gray;
                background: blue;
            }
        """)

        force.addWidget(force_checkbox, 1)

        # Splitter Config
        cfg_hsplit_Panel2 = QSplitter(Qt.Orientation.Horizontal)
        cfg_hsplit_Panel2.addWidget(box_chan)
        cfg_hsplit_Panel2.addWidget(w_p2)
        cfg_hsplit_Panel2.addWidget(w_p2s)
        cfg_hsplit_Panel2.addWidget(w_stmin)
        cfg_hsplit_Panel2.addWidget(w_stmax)
        cfg_hsplit_Panel2.addWidget(w_force)

        cfg_hsplit_Panel2.setChildrenCollapsible(False)
        QTimer.singleShot(0, lambda: cfg_hsplit_Panel2.setSizes([1, 1, 1, 1, 1]))

        # Bọc splitter cấu hình vào 1 QWidget + HBoxLayout
        box_channel = QWidget()
        lay_cfg_panel2 = QHBoxLayout(box_channel); lay_cfg_panel2.setContentsMargins(0,0,0,0)
        lay_cfg_panel2.addWidget(cfg_hsplit_Panel2)

        # ------- 3) Panel: Configuration -------
        # Req/Resp ID
        w_reqresp = QWidget()
        plus = QHBoxLayout(w_reqresp)
        plus.setContentsMargins(8,6,8,6) 
        plus.setSpacing(8)
        plus.addWidget(QLabel("Add ECU:"))

        # "+" button
        self.plus_btn = QPushButton("+")
        self.plus_btn.setMinimumHeight(30)
        self.plus_btn.setStyleSheet(self.get_button_style())
        plus.addWidget(self.plus_btn)
        self.plus_btn.clicked.connect(self.plus_action)

        # Load
        w_load = QWidget()
        load = QHBoxLayout(w_load); load.setContentsMargins(8,6,8,6)
        btnLoad = QPushButton("Load config")
        btnLoad.setMinimumHeight(30)
        btnLoad.setStyleSheet(self.get_button_style())
        btnLoad.clicked.connect(self._load_config)
        load.addWidget(btnLoad, 1)

        # Save
        w_save = QWidget()
        save = QHBoxLayout(w_save); save.setContentsMargins(8,6,8,6)
        btnSave = QPushButton("Save config")
        btnSave.setMinimumHeight(30)
        btnSave.setStyleSheet(self.get_button_style())
        btnSave.clicked.connect(self._save_config)
        save.addWidget(btnSave, 1)

        # Splitter Config
        cfg_hsplit = QSplitter(Qt.Orientation.Horizontal)
        cfg_hsplit.addWidget(w_reqresp)
        cfg_hsplit.addWidget(w_load)
        cfg_hsplit.addWidget(w_save)
        cfg_hsplit.setChildrenCollapsible(False)
        QTimer.singleShot(0, lambda: cfg_hsplit.setSizes([1, 1, 1]))

        # Bọc splitter cấu hình vào 1 QWidget + HBoxLayout
        box_cfg = QWidget()
        lay_cfg = QHBoxLayout(box_cfg); lay_cfg.setContentsMargins(0,0,0,0)
        lay_cfg.addWidget(cfg_hsplit)

        # ----------- Splitter -----------
        vsplit = QSplitter(Qt.Orientation.Vertical)
        vsplit.addWidget(title_CAN)     # 0: Title
        vsplit.addWidget(box_log)       # 1: Select CAN Log
        vsplit.addWidget(box_channel)   # 2: CAN Channel
        vsplit.addWidget(box_cfg)       # 3: Configuration
        vsplit.setChildrenCollapsible(False)

        QTimer.singleShot(0, lambda: vsplit.setSizes([1, 2, 2, 3]))

        vsplit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Maximum)
        wrapper = QWidget()
        out = QVBoxLayout(wrapper); out.setContentsMargins(0,0,0,0)
        out.addWidget(vsplit)
        return wrapper

    # ---------------------- UI: TOP-RIGHT CONTROLS ------------------------
    def build_top_right_controls(self) -> QWidget:
        root = QWidget()
        outer = QVBoxLayout(root); outer.setContentsMargins(0,0,0,0); outer.setSpacing(0)

        # --- CARD Background ---
        card = QFrame()
        card.setObjectName("rightCard")
        card.setStyleSheet("""
            #rightCard {
                background: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 10px;
            }
        """)
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(16)
        shadow.setOffset(0, 2)
        shadow.setColor(QColor(0, 0, 0, 40))
        card.setGraphicsEffect(shadow)

        inner = QVBoxLayout(card)
        inner.setContentsMargins(12, 12, 12, 12)   
        inner.setSpacing(10)

        # Row1: Analysis + Export format
        row1 = QHBoxLayout()

        self.is_analyzing = False

        self.btnAnalysis = QPushButton("▶  Analysis")
        self.btnAnalysis.setMinimumHeight(30)
        self.btnAnalysis.setStyleSheet(self.get_button_style())
        row1.addWidget(self.btnAnalysis)

        row1.addSpacing(8)
        row1.addWidget(QLabel("Format"))

        self.comboFormat = QComboBox()
        self.comboFormat.addItems(CommonData.SUPPORTED_EXPORT_FORMAT )
        self.comboFormat.setCurrentIndex(0)
        self.comboFormat.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
        """)

        row1.addWidget(self.comboFormat)

        row1.addSpacing(8)

        self.btnExport = QPushButton("Export")
        self.btnExport.setMinimumHeight(30)
        self.btnExport.setStyleSheet(self.get_button_style())
        row1.addWidget(self.btnExport)

        inner.addLayout(row1)

        # Row2: Progress bar + %
        row2 = QHBoxLayout()
        row2.addWidget(QLabel("Progress:"))
        self.progress = QProgressBar()
        self.progress.setRange(0, 100)
        row2.addWidget(self.progress, 1)
        self.lblRatio = QLabel("0 %")
        self.lblRatio.setFixedWidth(40)
        inner.addLayout(row2)

        outer.addWidget(card)

        # Handlers
        self.btnAnalysis.clicked.connect(self._on_analysis_clicked)
        self.btnExport.clicked.connect(self._export_clicked)

        return root
    
    # --------------------- UI: BOTTOM TABLE PANEL ---------------------
    def build_bottom_table_panel(self) -> QWidget:
        panel = QWidget(parent=self)
        v = QVBoxLayout(panel); v.setContentsMargins(0,0,0,0); v.setSpacing(8)

        title = QLabel("<b>Table Configuration</b>")
        title.setStyleSheet("font-size: 14pt; font-weight: bold;")
        v.addWidget(title)

        self.table = QTableView()
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        self.model = QStandardItemModel(0, 3, self)
        self.model.setHorizontalHeaderLabels([analysis_table["request"][0], analysis_table["response"][0], analysis_table["service"][0], analysis_table["delete"][0]])
        self.table.setModel(self.model)
        # self.table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.table.verticalHeader().setVisible(False)

        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)  # <-- thêm dòng này

        self.table.setColumnWidth(analysis_table["request"][1], 120)
        self.table.setColumnWidth(analysis_table["response"][1], 120)
        self.table.setColumnWidth(analysis_table["service"][1], 600)
        self.table.setColumnWidth(analysis_table["delete"][1], 120)
        self.table.setAlternatingRowColors(True)
        self.table.setStyleSheet("""
            QTableView {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #c0c0c0;
                font-weight: bold;
            }
            QTableView::item:selected:active { background: #1976d2; color: white; }
        """)

        v.addWidget(self.table, 1)

        return panel
    
    # -------------------------- FUNCTION --------------------------
    # LOAD CAN LOG
    def _browse_log(self):
        dlg = QFileDialog(self, "Select CAN log")
        dlg.setFileMode(QFileDialog.FileMode.ExistingFile)

        all_exts = CommonData.SUPPORTED_LOG_FORMAT
        filter_str = "CAN Log Files (" + " ".join(f"*{ext}" for ext in all_exts) + ")"
        print(filter_str)
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select CAN Log File",
            "",
            filter_str
        )
        
        self.editLogPath.setText(file_path)

    # LOAD AND SAVE CONFIG
    def _save_config(self):
        total_rows = self.model.rowCount()
        source_file = self.editLogPath.text()
        format_choice = self.get_save_format_preference()
        if format_choice is None:
            return  # User cancelled format selection

        # Get current timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if format_choice == "json":
            default_filename = f"cut_configuration_{timestamp}.json"
        else:
            default_filename = f"cut_configuration_{timestamp}.xlsx"

        default_path = os.path.join(source_file, default_filename)
        path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Configuration",
            default_path,
            self.config_manager.get_save_dialog_filter(format_choice)
        )

        if not path: return # User cancelled
        try:
            configurations = []

            for row_index in range(total_rows):
                req_widget = self.table.indexWidget(self.model.index(row_index, analysis_table["request"][1]))
                res_widget = self.table.indexWidget(self.model.index(row_index, analysis_table["response"][1]))
                req_text = req_widget.text().strip() if req_widget else ""
                res_text = res_widget.text().strip() if res_widget else ""
                service_widget = self.table.indexWidget(self.model.index(row_index, analysis_table["service"][1]))
                service_text = service_widget.text().strip() if service_widget else ""
                print(f"{row_index}: {req_text}, {res_text}, {service_text}")
                config = {
                    "config_num": row_index,
                    "request_id": req_text,
                    "respond_id": res_text
                }
                configurations.append(config)
            config_data = {
                "global_configuration": {
                    "channel": self.editChannel.text(),
                    "p2_ms": self.P2_value.text(),
                    "p2_star_ms": self.P2S_value.text(),
                    "stmin_ms": self.STmin_value.text(),
                    "stmax_ms": self.STmax_value.text(),
                    "output_format": self.comboFormat.currentText()
                },
                "configurations": configurations
            }
            self.config_manager.save_configuration(config_data, path, format_choice)        
        except PermissionError:
            QMessageBox.critical(self, 
                                 "Permission Error", 
                                 f"Cannot write to the selected file. Please check file permissions or choose a different location.\n\nFile: {path}")

        except Exception as e:
            QMessageBox.critical(self, 
                                 "Save Error", 
                                 f"Failed to save configuration:\n\n{str(e)}")


    def get_save_format_preference(self):
        """Ask user to choose save format"""
        formats = ["JSON (.json)", "Excel (.xlsx)"]
        format_choice, ok = QInputDialog.getItem(
            self, "Choose Format", "Select configuration file format:",
            formats, 0, False
        )

        if ok:
            return "json" if "JSON" in format_choice else "excel"
        return None

    def _load_config(self):
        path, _ = QFileDialog.getOpenFileName(self, "Load configuration", "", "JSON (*.json)")
        if not path: return
        try:
            with open(path, "r", encoding="utf-8") as f:
                meta = json.load(f)
            self.editLogPath.setText(meta.get("selected_log", ""))
            self.editChannel.setText(meta.get("channel", CommonData.DEFAULT_CHANNEL))
            self.P2_value.setText(str(meta.get("p2_ms", CommonData.DEFAULT_P2_MS)))
            self.P2S_value.setText(str(meta.get("p2star_ms", CommonData.DEFAULT_P2STAR_MS)))
            self.STmin_value.setText(str(meta.get("stmin_s", CommonData.DEFAULT_STDATA_MS)))
            self.STmax_value.setText(str(meta.get("stmax_s", CommonData.DEFAULT_STDATA_MS)))
            if "export_format" in meta:
                pass
            QMessageBox.information(self, "Loaded", f"Loaded from:\n{path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", str(e))

    def _collect_meta(self) -> dict:
        return {
            "selected_log": "",
            "channel": "",
            "p2_ms": "",
            "p2star_ms": "",
            "stmin_s": "",
            "stmax_s": "",
        }
    
    # VALIDATE
    def _validate_input_params(self) -> str:
        if self.model.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No ECU added yet")
            return "error"
        else:
            return "pass"

    def _set_analysis_running(self, running: bool):
        self.btnAnalysis.setEnabled(not running)
        self.btnExport.setEnabled(not running)      # tuỳ chọn: khoá Export khi chạy
        self.comboFormat.setEnabled(not running)    # tuỳ chọn: khoá Format
        self.btnAnalysis.setText("Running…" if running else "▶  Analysis")
        if running:
            QApplication.setOverrideCursor(Qt.CursorShape.BusyCursor)
        else:
            QApplication.restoreOverrideCursor()
    
    def _on_analysis_clicked(self):
        """Start analysis with modal progress dialog"""
        if self._validate_input_params() != "pass":
            self.progress.setValue(0)
            if hasattr(self, "lblRatio"): 
                self.lblRatio.setText("0 %")
            return
        
        # Create modal progress dialog
        self.progress_dialog = QProgressDialog(
            "Starting analysis...",  # Initial label text
            "Cancel",               # Cancel button text
            0,                      # Minimum value
            100,                    # Maximum value
            self                    # Parent
        )
        
        # Configure dialog
        self.progress_dialog.setWindowTitle("UDS Analysis Progress")
        self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress_dialog.setMinimumDuration(0)  # Show immediately
        self.progress_dialog.setMinimumWidth(450)
        self.progress_dialog.setMinimumHeight(120)
        
        # Disable the main UI
        self._set_analysis_running(True)

        self.progress_dialog.canceled.connect(self.on_cancel_clicked)
        self.progress_dialog.show()
        id_pairs: list[IDPair] = []
        rows = self.model.rowCount()
        for r in range(rows):
            # Get QLineEdit widgets from table
            req_widget = self.table.indexWidget(self.model.index(r, analysis_table["request"][1]))
            res_widget = self.table.indexWidget(self.model.index(r, analysis_table["response"][1]))
            
            # Extract text from widgets
            req_text = req_widget.text().strip() if req_widget else ""
            res_text = res_widget.text().strip() if res_widget else ""
            if not req_text or not res_text:
                continue
            try:
                req_val = int(req_text, 16)
                res_val = int(res_text, 16)
                id_pairs.append(IDPair(req_val, res_val))
            except ValueError:
                print(f"Skip invalid hex at row {r}: req='{req_text}', res='{res_text}'")
                continue
        
        if not id_pairs:
            raise ValueError("No valid Request/Response ID pairs found.")

        cfg = AnalyzerConfig(
            id_pairs=id_pairs,
            p2_ms=150, p2_star_ms=5000, channel="CAN 1",
            services=None,
        )
        self.analysis_requested.emit(cfg)

    @pyqtSlot()  # SLOT: called when progress dialog is cancelled
    def on_cancel_clicked(self):
        self.cancel_requested.emit()

    @pyqtSlot(int)  # SLOT: receives progress updates from model
    def update_progress(self, value):
        """Update the progress dialog"""
        if self.progress_dialog:
            self.progress_dialog.setValue(value)
            self.progress.setValue(value)
    
    @pyqtSlot()  # SLOT: receives completion message from model
    def on_analysis_completed(self):
        """Handle analysis completion"""
        # Close progress dialog
        if self.progress_dialog:
            self.progress_dialog.close()
        self.progress.setValue(100)
        
        # Re-enable button
        self._set_analysis_running(False)
    
    @pyqtSlot()  # SLOT: receives cancellation confirmation from model
    def on_analysis_cancelled(self):
        """Handle analysis cancellation"""
        # Close progress dialog
        if self.progress_dialog:
            self.progress_dialog.close()
        
        # Re-enable button
        self._set_analysis_running(False)
    
    # EXPORT 
    def _export_clicked(self):
        fmt = self.comboFormat.currentText().strip()

        reports_dir = Path("assets/reports"); reports_dir.mkdir(parents=True, exist_ok=True)
        out_xlsx = reports_dir / f"report{fmt}"
        # TODO: uncomment this line when Model and Service related to UDS analysis are added
        # self.analyzer.export_report(str(out_xlsx))

        if out_xlsx.exists():
            QMessageBox.information(self, "Export", f"Exported results in format:\n{fmt}")
        else:
            QMessageBox.information(self, "Fail", f"Haven't Analyzed")

    # ADD CONFIG
    def plus_action(self):
        # Check if any files are selected
        if self.editLogPath.text().strip() == "":
            QMessageBox.warning(self, "Warning", "No files chosen yet")
            return
        
        self.add_command_row()

    def add_command_row(self):
        """Add a new command row to the cutting table model with proper widgets"""
        # Create a new row with default values
        row_items = [
            QStandardItem(""),
            QStandardItem(""),
            QStandardItem(""),
            QStandardItem(""),
        ]

        # Make text items editable
        for i, item in enumerate(row_items):
            if i in [analysis_table["request"][1], analysis_table["response"][1]]:  # Request ID, Response ID
                item.setEditable(True)
            else:
                item.setEditable(False)

        # Add the row to the model
        self.model.appendRow(row_items)

        # Get the new row index
        new_row_index = self.model.rowCount() - 1

        # Create and set custom widgets for specific columns
        self.create_row_widgets(new_row_index)

        # Scroll to the newly added row
        model_index = self.model.index(new_row_index, 0)
        self.table.scrollTo(model_index)

    def create_row_widgets(self, row_index):
        self.requestID = QLineEdit()
        self.requestID.setText("0x000")
        self.requestID.editingFinished.connect(lambda: self.on_hex_input_finished(row_index))

        self.responseID = QLineEdit()
        self.responseID.setText("0x000")
        self.responseID.editingFinished.connect(lambda: self.on_hex_input_finished(row_index))

        id_list = CommonData.DEFAULT_UDS_IDS

        # widget
        self.cell = QWidget(self.table)
        self.lay = QHBoxLayout(self.cell)
        self.lay.setContentsMargins(0, 0, 0, 0)
        self.lay.setSpacing(6)

        # --- combo
        combo = MultiSelectCombo(id_list, include_all=True)
        combo.setObjectName("reqRespCombo")
        combo.setMinimumHeight(32)
        combo.setFixedWidth(150)
        combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        combo.setStyleSheet("""
            #reqRespCombo {
                background: white; border: 1px solid #c0c0c0; border-radius: 6px;
                padding: 2px 28px 2px 8px;
            }
            #reqRespCombo::drop-down { width: 24px; }
            #reqRespCombo QAbstractItemView {
                background: white; border: 1px solid #101010; outline: 0;
            }
            #reqRespCombo QAbstractItemView::item { padding: 4px 6px; }
            #reqRespCombo QAbstractItemView::indicator {
                width: 18px; height: 18px; border: 1px solid #7a7a7a; border-radius: 5px; background: #ffffff; image: none;
                margin: 0 8px 0 2px;
            }
            #reqRespCombo QAbstractItemView::indicator:checked {
                background: #1e88e5; border: 1px solid #1565c0;
            }
        """)

        # --- label 
        label = QLabel("")
        label.setObjectName("selectedIdsLabel")
        label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        label.setStyleSheet("""
            #selectedIdsLabel { border: 1px solid #e0e0e0; border-radius: 6px; padding: 4px 6px; }
        """)

        text_ini = ", ".join(map(str, id_list))
        label.setText(str(text_ini))

        combo.selectionChanged.connect(lambda: self.update_ids(combo, label))

        self.lay.addWidget(combo, 2)
        self.lay.addWidget(label, 3)

        self.delete_button = QPushButton("Delete")
        self.delete_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336; color: white; border-radius: 15px;
                font-weight: bold; border: none; padding: 5px 10px;
            }
            QPushButton:hover { background-color: #da190b; }
        """)
        self.delete_button.clicked.connect(lambda: self.delete_command_row_from_table_new(row_index))

        self.table.setIndexWidget(self.model.index(row_index, analysis_table["request"][1]), self.requestID)
        self.table.setIndexWidget(self.model.index(row_index, analysis_table["response"][1]), self.responseID)
        self.table.setIndexWidget(self.model.index(row_index, analysis_table["service"][1]), self.cell)
        self.table.setIndexWidget(self.model.index(row_index, analysis_table["delete"][1]), self.delete_button)

        return
    
    def update_ids(self, combo, label):
        if hasattr(combo, "selected_ids"):
            ids = combo.selected_ids()
        elif hasattr(combo, "checkedItems"):
            ids = list(combo.checkedItems())
        else:
            ids = [t.strip() for t in combo.currentText().split(",") if t.strip()]

        text = ", ".join(map(str, ids))
        label.setText(text)
        label.setToolTip(text)

    def delete_command_row_from_table_new(self, row_index):
        """Delete a command row from the table model"""
        reply = QMessageBox.question(self, "Confirm Delete",
                                   "Are you sure you want to delete this row?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # Remove the row from the model (this will also remove the widgets)
            self.model.removeRow(row_index)
            # Update button connections for remaining rows since row indices have changed
            self.update_button_connections_after_delete(row_index)

    def update_button_connections_after_delete(self, deleted_row_index):
        """Update button connections after a row is deleted"""
        # After deleting a row, all rows below it have their indices shifted up by 1
        # We need to reconnect the buttons with the correct row indices
        total_rows = self.model.rowCount()

        for row in range(deleted_row_index, total_rows):
            # Reconnect Delete button and Editted input 
            req_widget = self.table.indexWidget(self.model.index(row, analysis_table["request"][1]))
            res_widget = self.table.indexWidget(self.model.index(row, analysis_table["response"][1]))
            delete_button = self.table.indexWidget(self.model.index(row, analysis_table["delete"][1]))
            if delete_button:
                # Disconnect old connection and connect with new row index
                delete_button.clicked.disconnect()
                delete_button.clicked.connect(lambda _, r=row: self.delete_command_row_from_table_new(r))
                req_widget.editingFinished.disconnect()
                req_widget.editingFinished.connect(lambda r=row: self.on_hex_input_finished(r))
                res_widget.editingFinished.disconnect()
                res_widget.editingFinished.connect(lambda r=row: self.on_hex_input_finished(r))

    def on_hex_input_finished(self, row_index):
        req_widget = self.table.indexWidget(self.model.index(row_index, analysis_table["request"][1]))
        res_widget = self.table.indexWidget(self.model.index(row_index, analysis_table["response"][1]))
        text_req = req_widget.text().strip() if req_widget else ""
        text_res = res_widget.text().strip() if res_widget else ""
        try:
            value_req = int(text_req, 16)
            if value_req < 0x000 or value_req > 0xFFF:
                req_widget.setText("0x000")
        except ValueError:
            req_widget.setText("0x000")
        try:
            value_res = int(text_res, 16)
            if value_res < 0x000 or value_res > 0xFFF:
                res_widget.setText("0x000")
        except ValueError:
            res_widget.setText("0x000")
            